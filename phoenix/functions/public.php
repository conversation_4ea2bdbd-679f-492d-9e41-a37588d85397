<?php

// Print error
function printError($errorCode, $errorDescription): never
{
    // Output: Prints a JSON with matching error code.
    $obj = (object)[
        'status' => 'error',
        'errorCode' => $errorCode,
        'errorDescription' => $errorDescription
    ];

    echo json_encode($obj);
    exit;
}

// Check if blog
function is_blog()
{
    global $wp_query;
    return (get_post_type() == 'post' || is_category() || $wp_query->is_search || is_home());
}

// Check if campaign
function is_campaign()
{
    return (in_array(get_post_type(), [ACQUISITION_SLUG, DYNAMIC_SLUG, CAMPAIGN_SLUG, START_PAGE_SLUG, SEO_PAGE_SLUG]) || is_page_template('page-qrcampaign.php') ||  is_page_template('page-dynamic-blocks.php'));
}

function is_game()
{
    return (in_array(get_post_type(), [LETTER_GAME_SLUG, QUIZ_SLUG]));
}

function is_responsible_gaming()
{
    return (get_post_type() == RESPONSIBLE_GAMING_SLUG);
}
function is_help_page()
{
    // Check for help page
    $result = get_post_type() == HELP_PAGE_SLUG;

    // Check for help topic (category)
    if(!$result) {
        if(!empty(get_queried_object()->taxonomy)) {
            $slug = get_queried_object()->taxonomy;
            $result = $slug === HELP_TOPIC_SLUG;
        }
    }

    return $result;
}

function is_ad_banner()
{
    return (get_post_type() == AD_BANNER_SLUG);
}

// String to hex
// Required function for japanese titled modals to generate slug
function strToHex($string) {
    $hex = '';
    for ($i = 0; $i < strlen((string) $string); $i++) {
        $hex .= dec2hex(ord($string[$i])) . '';
    }
    return $hex;
}

function dec2hex($number) {
    $hexvalues = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'];
    $hexval = '';
    while ($number != '0') {
        $hexval = $hexvalues[bcmod((string) $number, '16')] . $hexval;
        $number = bcdiv((string) $number, '16', 0);
    }
    return $hexval;
}

function includeLottie()
{
    wp_enqueue_script('lottiePlayer', get_parent_theme_file_uri() . '/assets/lottie-player.js', [], '1.3.1', true); //https://unpkg.com/browse/@lottiefiles/lottie-player@1.3.1/dist/lottie-player.js
}

function includeGSAP()
{
    // The core GSAP library
    wp_enqueue_script( 'gsap-js', 'https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js', array(), false, true );
    // ScrollTrigger - with gsap.js passed as a dependency
    wp_enqueue_script( 'gsap-scroll-trigger', 'https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/ScrollTrigger.min.js', array('gsap-js'), false, true );
}

/**
 * Add inline style for acf background component
 *
 * @param string $styleSelector = '.campaign .background'
 * @param array $backgroundGroup = get_field(acf component)
 * @return string
 */
function addBackgroundInlineStyle($styleSelector, $backgroundGroup)
{
    $backgroundType  = (!empty($backgroundGroup['background_type']) ? $backgroundGroup['background_type'] : 'background_image');
    $background = '';
    $mobileBackground = '';

    // If background color is set
    if (!empty($backgroundGroup['background_color'])) {
        $background .= 'background-image:none;background-color:' . $backgroundGroup['background_color'] . ';';
    }

    switch ($backgroundType) {
        case 'background_image':
            if (!empty($backgroundGroup['background_image'])) {
                $background .= "background-image:url(" . $backgroundGroup['background_image'] . ");";
            }

            if (!empty($backgroundGroup['mobile_background_image'])) {
                $mobileBackground = "background-image:url(" . $backgroundGroup['mobile_background_image'] . ");";
            }

            // If background options are set
            if (!empty($backgroundGroup['background_options'])) {
                $background .= 'background-size:' . $backgroundGroup['background_size'] . ';';
                $background .= 'background-position:' . $backgroundGroup['background_alignment'] . ';';
                $mobileBackground .= 'background-size:' . $backgroundGroup['mobile_background_size'] . ';';
                $mobileBackground .= 'background-position:' . $backgroundGroup['mobile_background_alignment'] . ';';
            }
        break;

        case 'background_pattern':
            if (!empty($backgroundGroup['background_image'])) {
                $background .= 'background-image:url(' . $backgroundGroup['background_image'] . ');background-size:auto;background-repeat:repeat;';
            }
        break;

        case 'background_gradient':
            $background .= 'background:' . $backgroundGroup['first_background_gradient_color'] . ';';
            $background .= 'background:linear-gradient(' . $backgroundGroup['background_gradient_angle'] .'deg, ' . $backgroundGroup['first_background_gradient_color'] . ' 0%, ' . $backgroundGroup['second_background_gradient_color'] . ' 100%);';
        break;

        case 'background_video':
            if (!empty($backgroundGroup['background_options'])) {
                $background .= 'object-fit:' . ($backgroundGroup['background_size'] === "auto" ? 'fill': $backgroundGroup['background_size']) . ';';
                $mobileBackground .= 'object-fit:' . ($backgroundGroup['mobile_background_size'] === "auto" ? 'fill': $backgroundGroup['mobile_background_size']) . ';';
            }
        break;
    }

    global $backgroundStyle;
    if (!empty($background)) {
        $backgroundStyle .= $styleSelector .'{'.$background.'}';

        if (!empty($mobileBackground)) {
            $backgroundStyle .= '@media screen and (max-width:480px){'. $styleSelector .'{'.$mobileBackground.'}}';
        }


        // Handle background size rules for Video Background
        if($backgroundType === 'background_video') {
            $styleSelector = $styleSelector . ' video';

            $backgroundStyle .= $styleSelector .'{'.$background.'}';

            if (!empty($mobileBackground)) {
                $backgroundStyle .= '@media screen and (max-width:768px){'. $styleSelector .'{'.$mobileBackground.'}}';
            }
        }
    }

}

/**
 * Alter existing table in the database
 *
 * @param string $table_name = 'comeon_beat_the_experts'
 * @param array $newColumns = ['email' => 'varchar(255)', 'odds' => 'varchar(255)'];
 */
function alter_table($table_name, $newColumns)
{
    foreach ($newColumns as $column => $type) {
        global $wpdb;
        // If $column is missing in the $table_name
        if ($wpdb->get_var("SHOW COLUMNS FROM `$table_name` LIKE '$column'") != $column) {
            do_action('qm/warning', "DB: Cannot find `$column` in table `$table_name`");

            $sql = "ALTER TABLE `$table_name` ADD `$column` $type";
            if ($wpdb->query($sql)) {
                do_action('qm/notice', "DB: Successfully added `$column` in table `$table_name`");
            }
        }
    }
}

function get_the_primary_term($postID, $taxonomy = 'category') {
    $terms = get_the_terms($postID, $taxonomy);

    if (is_array($terms)) {
        return array_shift($terms);  //get the first category
    }

    return null;
}

function getLowestPlayerSegmentValue() {
    return PLAYER_SEGMENTS['inactive'];
}

function getContentForSegmentValue($segmentedOffers) {
    $playerSegmentValue = player()->getSegmentValue($segmentedOffers['player_segment_type']);

    if (in_array($playerSegmentValue, PLAYER_SEGMENTS)) {
        $playerSegmentHighEnough = false;

        // we iterate the PLAYER_SEGMENTS from high to low
        foreach (PLAYER_SEGMENTS as $segmentValue) {
            // check if player current segment is high enough to assign the offer content
            if ($playerSegmentHighEnough || $playerSegmentValue == $segmentValue) {
                $playerSegmentHighEnough = true;

                $offerType = $segmentValue . '_offer';
                // check if content exist for segment
                // we check for the title cause that is required field in acf
                if (
                    !empty($segmentedOffers[$offerType]['offer_content']['campaign_header']) ||  // interactive, wheel
                    !empty($segmentedOffers[$offerType]['content']['campaign_header']) || // left-and-right
                    !empty($segmentedOffers[$offerType]['image']['image_title']) || // offer-of-the-day
                    !empty($segmentedOffers[$offerType]['header']) // calendar-offers
                ) {
                    return $segmentedOffers[$offerType];
                }
            }
        }
    }

    // if no offer found for the player current segment return the lowest segment offer
    $offerType = getLowestPlayerSegmentValue() . '_offer';
    return $segmentedOffers[$offerType];

}

// Validate IP address
function isValidIP($ip) {
    if (
        !filter_var(
            $ip,
            FILTER_VALIDATE_IP,
            FILTER_FLAG_IPV4 | FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE
        )
        && !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6 | FILTER_FLAG_NO_PRIV_RANGE)
    ) {
        return false;
    }

    return true;
}

// Get current user IP from headers
function getIP() {
    $remoteKeys = [
        'X-Worker-Real-IP',
        'CF-Connecting-IP',
        'HTTP_X_FORWARDED_FOR',
        'HTTP_CLIENT_IP',
        'HTTP_X_FORWARDED',
        'HTTP_FORWARDED_FOR',
        'HTTP_FORWARDED',
        'REMOTE_ADDR',
        'HTTP_X_CLUSTER_CLIENT_IP',
    ];

    foreach ($remoteKeys as $key) {
        if ($address = getenv($key)) {
            foreach (explode(',', $address) as $ip) {
                if (isValidIP($ip)) {
                    do_action('qm/info', 'getIP -> ' . $ip);

                    return $ip;
                }
            }
        }
    }

    return '*********';
}

// Get current user country code XX from headers
// If no headers are found, then perform a local check
function getCountryCode() {
    if (isset($_SERVER)) {
        // Key below refers to visitor's country data
        if (array_key_exists('HTTP_CF_IPCOUNTRY', $_SERVER)) {
            $response = $_SERVER['HTTP_CF_IPCOUNTRY'];
        } elseif (array_key_exists('HTTP_X_COUNTRYCODE', $_SERVER)) {
            $response = $_SERVER['HTTP_X_COUNTRYCODE'];
        } elseif (getenv('HTTP_X_FORWARDED_COUNTRY')) {
            /* Since PHP supports white space in array keys,
            we can flip the array and use isset() to check the existence of value,
            it is much faster than array_search() or in_array() */
            $countryNamesAsKey = array_flip(COUNTRIES);
            $response          = $countryNamesAsKey[getenv('HTTP_X_FORWARDED_COUNTRY')] ?? false;
        }

        if(empty($response)) {
            $api = new Api('http://geoip:8080/json/' . getIP());
            $response = $api->get();

            // Fallback if http://geoip:8000 alias fails
            if(empty($response)) {
                $api = new Api('https://www.comeon.com/geoip');
                $response = $api->get();
            }

            if (!empty($response['country_code'])) {
                $response = $response['country_code'];
            } elseif (!empty($response['result'])) {
                if (is_string($response['result'])) {
                    $response['result'] = json_decode($response['result'], true);
                }
                if (!empty($response['result']['countryCode'])) {
                    $response = $response['result']['countryCode'];
                }
            }
        }
        
        if($response === 'CA') {
            $api = new Api('http://ip-api.com/json/' . getIP());
            $response = $api->get();

            if($response['region'] == 'ON') {
                do_action('qm/debug', 'Ontario exception triggered');
                $response = 'ON';
            }
        }
    }

    do_action('qm/info', 'getCountryCode() -> ' . $response);

    return $response;
}

function array_clear($array) {
    if (is_array($array)) {
        $array = array_map('array_clear', $array);
        return array_filter($array);
    }
    return $array;
}

function all_empty(...$variables) {
    return empty(array_filter($variables));
}

function at_least_one_has_value(...$variables) {
    return !all_empty($variables);
}

function all_have_values(...$variables) {
    return count($variables) == count(array_filter($variables));
}

function array_keys_have_values($array = [], $keys = []) {
    if (!all_have_values($array, $keys)) return false;
    foreach($keys as $key) {
        $result[$key] = $array[$key] ?: '';
    }
    return all_have_values($result);
}

// Used for decryption of PID (player id)
function decryptedPID($stringToDecrypt, $key = "Gpz01wQsgEXz23cQ") {

    // Bail early if string length is less than 32 characters
    if(strlen($stringToDecrypt) < 32) {
        return $stringToDecrypt;
    }

    /* AES decryption key is stored in ETCD,
        since we cant fetch ETCD via cloudways, we have to use it hardcoded
        Just for future reference, it is stored here
        /SYSTEM/PLAYER/ID_ENCRYPT_KEY
    */
    try {
        // Set the key and method
        $method = 'AES-128-ECB';

        // Convert hex to binary
        $encryptedData = hex2bin($stringToDecrypt);

        // Decrypt the data
        $decrypted = openssl_decrypt($encryptedData, $method, $key, OPENSSL_RAW_DATA);

        return $decrypted;
    } catch (Exception $e) {
        error_log("Can not decrypt stringToDecrypt={$stringToDecrypt}: " . $e->getMessage());
        return null;
    }
}

// Used for encryption of PID (player id)
function encryptedPID($stringToEncrypt, $key = "Gpz01wQsgEXz23cQ") {

    // Bail early if string length is 32 characters or more
    if(strlen($stringToEncrypt) >= 32) {
        return $stringToEncrypt;
    }

    try {
        // Set the key and method
        $method = 'AES-128-ECB';

        // Encrypt the data
        $encrypted = openssl_encrypt($stringToEncrypt, $method, $key, OPENSSL_RAW_DATA);

        // Convert to hex
        return bin2hex($encrypted);
    } catch (Exception $e) {
        error_log("Can not encrypt stringToEncrypt={$stringToEncrypt}: " . $e->getMessage());
        return null;
    }
}

function uploadMediaFromUrl($url, $filename = '') {

    // Download url to a temp file
    $tmp = download_url($url);
    if (is_wp_error($tmp)) return false;

    // Get the filename and extension ("photo.png" => "photo", "png")
    $filename = $filename ?: pathinfo($url, PATHINFO_FILENAME);
    $extension = pathinfo($url, PATHINFO_EXTENSION);

    // An extension is required or else WordPress will reject the upload
    if (! $extension) {
        // Look up mime type, example: "/photo.png" -> "image/png"
        $mime = mime_content_type($tmp);
        $mime = is_string($mime) ? sanitize_mime_type($mime) : false;

        // Only allow certain mime types because mime types do not always end in a valid extension (see the .doc example below)
        $mime_extensions = [
            // mime_type         => extension (no period)
            'image/jpg'          => 'jpg',
            'image/jpeg'         => 'jpeg',
            'image/gif'          => 'gif',
            'image/png'          => 'png',
        ];

        if (isset($mime_extensions[$mime])) {
            // Use the mapped extension
            $extension = $mime_extensions[$mime];
        } else {
            // Could not identify extension. Clear temp file and abort.
            wp_delete_file($tmp);
            return false;
        }
    }

    // Upload by "sideloading": "the same way as an uploaded file is handled by media_handle_upload"
    // Do the upload
    $attachment_id = media_handle_sideload([
        'name' => "$filename.$extension",
        'tmp_name' => $tmp,
    ]);

    // Clear temp file
    wp_delete_file($tmp);

    // Error uploading
    if (is_wp_error($attachment_id)) return false;

    // Success, return attachment ID
    return (int) $attachment_id;
}