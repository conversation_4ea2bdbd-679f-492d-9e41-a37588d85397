/* Lint ------------------- */
/* global documentReady, get<PERSON><PERSON>ie, dataL<PERSON>er<PERSON><PERSON><PERSON>, fetchTrackingWP, main_params */

documentReady(function () {
    let templateName,
        isGoSite  = main_params.site == ('go'),
        isRgSite  = main_params.site == ('rg'),
        isFaqSite = main_params.site == ('faq');

    let bodyClasses  = document.body.classList;
    let isPage       = bodyClasses.contains('article') || bodyClasses.contains('page');
    let isDynamic    = bodyClasses.contains('dynamic');
    let isStartPage  = bodyClasses.contains('start-page');
    let isPNP        = bodyClasses.contains('pnp');
        templateName = bodyClasses[1] || bodyClasses[0];

    // handle templates: dynamic, dynamic-start-page, normal-start-page
    templateName = (isDynamic ? "dynamic" :  templateName);
    templateName = (isStartPage ? (isPNP ? bodyClasses[2] : bodyClasses[1]) + "-start-page" : templateName);

    // handle pages: e.g. page-go, page-rg, page-faq
    templateName = (isPage ? "page-go" : templateName); // hardcode fallback for Go pages
    templateName = (isPage && (isGoSite || isRgSite || isFaqSite) ? "page-" + main_params.site : templateName);

    let brand_root = window.location.hostname.split('.').slice(-2).join('.');

    // Define missing cookies
    if(!getCookie('locale')) {
        document.cookie = "locale=" + main_params.locale + ";domain=" + brand_root + ";path=/";
    }

    // Fetch WP tracking API
    if(getCookie('locale')) {
        fetchTrackingWP();
    }

    var trackEventTriggerElements = [
        document.querySelectorAll('.js-button-claim'),
        document.querySelectorAll('.js-button-login'),
        document.querySelectorAll('.js-button-register'),
        document.querySelectorAll('.footer a'),
        document.querySelectorAll('.navigation__container--promo .menu-item'), // Header Menu links on desktop
        document.querySelectorAll('.navigation__menu--mobile-tabs a'), // Global Nav Menu links on mobile
    ];

    trackEventTriggerElements.forEach((elements, index) => {
        if (elements.length > 0) {
            elements.forEach((element) => {
                element.addEventListener("click", function (clickEvent) {
                    let eventCategory = '';
                    let eventAction   = 'Click'
                    let eventLabel    = ''
                    let eventValue    = ''; //(element.href.length && element.href);

                    let body   = document.querySelector('body');
                    let parent = element.parentElement;

                    // Define category and action according to CTA typecase 'sportsbook':
								return (
									<FilterLink
										key={`navbar-${product}`}
										data-at="sportsbook"
										href={ROUTE_SPORTSBOOK}
										activeMatch={ROUTE_SPORTSBOOK}
										onClick={() => {
											insertTrackEvent({
												category: 'Top Nav Item',
												action: 'Click',
												label: 'sportsbook'
											});
											sportsbookNavigation.navigateToProduct('sportsbook');
										}}
									>
										{translations.MOBILE_MAIN_MENU_SPORTS}
									</FilterLink>
								);
							case 'live-betting':
								return (
									<FilterLink
										key={`navbar-${product}`}
										data-at="live-betting"
										href={ROUTE_LIVEBETTING}
										activeMatch={ROUTE_LIVEBETTING}
										onClick={() => {
											insertTrackEvent({
												category: 'Top Nav Item',
												action: 'Click',
												label: 'live-betting'
											});
											sportsbookNavigation.navigateToProduct('livebetting');
											// insertInitialMetaEvent(product);
										}}
									>
										{translations.MOBILE_MAIN_MENU_LIVE_SPORTS}
									</FilterLink>
								);
							case 'casino':
								return (
									<FilterLink
										key={`navbar-${product}`}
										data-at="casino"
										activeMatch={ROUTE_CASINO}
										href={ROUTE_CASINO_EXPLORE}
										onClick={() => {
											insertTrackEvent({
												category: 'Top Nav Item',
												action: 'Click',
												label: 'casino'
											});
											// insertInitialMetaEvent(product);
										}}
									>
										{translations.MOBILE_MAIN_MENU_CASINO}
									</FilterLink>
								);

							case 'livecasino': {
								return (
									<FilterLink
										key={`navbar-${product}`}
										data-at="live-casino"
										activeMatch={ROUTE_LIVE_CASINO}
										href={ROUTE_LIVE_CASINO_EXPLORE}
										onClick={() => {
											insertTrackEvent({
												category: 'Top Nav Item',
												action: 'Click',
												label: 'live-casino'
											});
											// insertInitialMetaEvent(product);
										}}
										retainCase
									>
										{translations.MOBILE_LIVE_CASINO_BUTTON}
									</FilterLink>
								);
							}
							case 'we-spin':
								return (
									<FilterLink
										key={`navbar-${product}`}
										data-at="wespin"
										retainCase
										className="u-relative"
										href={ROUTE_WE_SPIN}
										onClick={() => {
											insertTrackEvent({
												category: 'Top Nav Item',
												action: 'Click',
												label: 'we-spin'
											});
											// insertInitialMetaEvent(product);
										}}
									>
										{translations.MOBILE_MAIN_MENU_WESPIN}
									</FilterLink>
								);
							case 'virtuals':
								return (
									<FilterLink
										key={`navbar-${product}`}
										href={ROUTE_VIRTUALS}
										data-at={ROUTE_VIRTUALS}
										retainCase
										activeMatch={ROUTE_VIRTUALS}
										onClick={() => {
											insertTrackEvent({
												category: 'Top Nav Item',
												action: 'Click',
												label: 'virtuals'
											});
										}}
									>
										{translations.PRODUCT_NAVBAR_VIRTUALS}
									</FilterLink>
								);
							case 'tvbet':
								return (
									<FilterLink
										key={`navbar-${product}`}
										data-at="tvbet"
										href={ROUTE_TVBET}
										activeMatch={ROUTE_TVBET}
										retainCase
										onClick={() => {
											insertTrackEvent({
												category: 'Top Nav Item',
												action: 'Click',
												label: 'tvbet'
											});
										}}
									>
										{translations.MOBILE_MAIN_MENU_TVBET}
									</FilterLink>
								);



                    // Desktop header menu and Mobile menu, (which is called "global nav" during implementation, it's a tabbed menu)
                    if (element.classList.contains('menu-item') !== null) {
                        eventCategory = 'Top Nav Item';
                        eventAction   = 'Click';
                        // Use data-gtm-key attribute when found, fallback to text content
                        eventLabel    = clickEvent.target.getAttribute('data-gtm-key') || clickEvent.target.getAttribute('title') || clickEvent.target.text?.toLowerCase();
                    }

                    // Product icons on Start Page
                    if (clickEvent.target.closest('.sp-block-products__item') !== null) {
                        eventCategory = 'product buttons';
                        eventAction   = 'Click';
                        eventLabel    = clickEvent.target.text;
                    }

                    if (element.classList.contains('js-button-claim')) {
                        eventCategory = 'Offer';
                        eventAction   = 'Claim';
                    }

                    if (element.classList.contains('js-button-register')) {
                        eventCategory = 'Registration';
                        eventAction = 'Click';
                    }

                    if (element.classList.contains('js-button-login')) {
                        eventCategory = 'Login';
                        eventAction   = 'Log-In';
                    }

                    if (element.classList.contains('footer a')) {
                        eventCategory = 'Footer';
                        eventAction   = 'promotions';
                    }


                    // If header CTAs
                    if(parent.classList.contains('login-wrapper') || parent.classList.contains('navigation__login-mobile')) {
                        eventLabel = 'Header';
                    }

                    // Define final variables
                    eventAction = eventAction.length ? eventAction : 'Click';
                    eventLabel = eventLabel.length ? eventLabel : `Wordpress - ${eventLabel} - ${templateName}`;

                    // If start page template
                    // if(body.classList.contains('start-page')) {
                    //     eventAction = 'Start';
                    // }

                    // Handle the dataLayer info
                    dataLayerHandler({
                        'event'        : 'trackEvent',
                        'eventCategory': eventCategory,
                        'eventAction'  : eventAction,
                        'eventLabel'   : eventLabel,
                        'eventValue'   : eventValue
                    });
                });
            });
        }
    });
});
