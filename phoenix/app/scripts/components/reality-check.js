/* Lint ------------------- */
/* global documentReady, getCookie, main_params, fetchForPlayer */

// Reality Check (RC) - Redirect to main site if RC popup already showed up to player on main site

documentReady(function () {

    if (main_params.loggedin && !main_params.simulated_user) {
        let RCenabled = false,
            RCtimeEnd = 0,
            RCtimeleft = 0;

        RCtimeEnd = getCookie('RCETU');
        if (RCtimeEnd) {
            RCenabled = true;
            RCtimeleft = parseInt(RCtimeEnd - new Date());
        }

        fetchForPlayer('/reality-check/notifications/next')
            .then(response => {
                if (response.status === 'SUCCESS') {
                    RCenabled = response.realityCheck.id;
                    RCtimeEnd = response.notification.endTime;
                    RCtimeleft = response.notification.timeLeftInMillis;
                }
            });

        if (RCenabled) {
            setTimeout(function () {
                RCtimeleft -= 1000;
                if (RCtimeleft < 0) {
                    window.location.replace(main_params.brand_url);
                }
            }, 1000);
        }
    }
});
