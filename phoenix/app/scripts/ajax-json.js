/* Lint ------------------- */
/* global main_params, getCookie */

function sendJson(path, data, httpMethod, domain) {
	const headers = new Headers();
	headers.append('Content-Type', 'application/json');
	const options = {
		headers,
		method: httpMethod,
		mode: 'cors',
		cache: 'default',
		credentials: 'include',
		body: JSON.stringify(data)
	};

	return fetch(getUrl(path, domain), options)
		.then(response => {
			if (response.status === 204) {
				return undefined;
			}
			return response.json();
		})
		.catch(e => {
			// eslint-disable-next-line no-console
			console.log(`${e} for ajax call on ${httpMethod} ${path}`);

			return Promise.reject(e);
		});
}

function postJson(path, data, domain) {
	return sendJson(path, data, 'POST', domain);
}

function fetchForPlayer(path, domain) {
	const headers = new Headers();
	headers.append('Cookie', `sessionId=${getCookie('sessionId')}`);

	const options = {
		headers,
		method: "GET",
		mode: 'cors',
		cache: 'default',
	};

	return fetch(getUrl(path, domain), options).then(response => {
		console.log(`response ${response} for ${path}`);
		if (response.status === 204) {
			return undefined;
		}
		return response.json();
	})
		.catch(e => {
			// eslint-disable-next-line no-console
			console.log(`${e} for ajax call on ${path}`);

			return Promise.reject(e);
		});
}

const createUrlParameters = urlParams =>
	Object.keys(urlParams)
		.map(key => {
			const encodedKey = encodeURIComponent(key);
			const encodedDataKey = encodeURIComponent(urlParams[key]);
			return `${encodedKey}=${encodedDataKey}`;
		})
		.join('&');

function getUrl(path, domain, urlParams) {
	const API_BASE_PATH = domain || main_params.brand_url || '';
	if (urlParams === undefined) {
		return `${API_BASE_PATH}${path}`;
	}
	return `${API_BASE_PATH}${path}?${createUrlParameters(urlParams)}`;
}
