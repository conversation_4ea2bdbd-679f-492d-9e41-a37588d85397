<?php
global $syncFooter, $settingsOverAPI;

$footerLeftArea = get_field_tweaked('footer_left_area', 'options');
$footerRightArea = get_field_tweaked('footer_right_area', 'options');
?>
<div class="nav-menus-container">
    <?php if (!empty($syncFooter) && !empty($settingsOverAPI['footerLinks'])) : ?>
        <?php
        $footerLinksCountInOneColumn = 0;

        foreach ($settingsOverAPI['footerLinks'] as $linkGroup) {
            if ($linkGroup['title'] == ucfirst((string) CURRENT_BRAND)) { // use the group links from the seo links with the brand name column
                $menuItems = $linkGroup['items'];
                $footerLinksCountInOneColumn = ceil((count($menuItems) / 2));
            }
        }
        ?>
        <?php if (!empty($menuItems)) : ?>
            <ul class="nav-menu">
                <?php foreach ($menuItems as $linkIndex => $link) :
                    if ($linkIndex < $footerLinksCountInOneColumn) : ?>
                        <li>
                            <a href="<?= $link['url'] ?>"><?= $link['text'] ?><i class="nav-menu-arrow"><?= vector('right'); ?></i></a>
                        </li>
                <?php endif;
                endforeach; ?>
            </ul>
            <ul class="nav-menu">
                <?php foreach ($menuItems as $linkIndex => $link) :
                    if ($linkIndex >= $footerLinksCountInOneColumn) : ?>
                        <li>
                            <a href="<?= $link['url'] ?>"><?= $link['text'] ?><i class="nav-menu-arrow"><?= vector('right'); ?></i></a>
                        </li>
                <?php endif;
                endforeach; ?>
            </ul>
        <?php endif; ?>
    <?php else : ?>
        <?php if (!empty($footerLeftArea)) : ?>
            <ul class="nav-menu">
                <?php foreach ($footerLeftArea as $link) : ?>
                    <li>
                        <a href="<?= $link['item_url'] ?>"><?= $link['item_text'] ?><i class="nav-menu-arrow"><?= vector('right'); ?></i></a>
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>
        <?php if (!empty($footerRightArea)) : ?>
            <ul class="nav-menu">
                <?php foreach ($footerRightArea as $link) : ?>
                    <li>
                        <a href="<?= $link['item_url'] ?>"><?= $link['item_text'] ?><i class="nav-menu-arrow"><?= vector('right'); ?></i></a>
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>
    <?php endif; ?>
</div>