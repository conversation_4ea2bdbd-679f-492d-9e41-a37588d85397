<?php
global $syncFooter, $settingsOverAPI;
$IsSEOLinksEnabled = get_field_tweaked('show_seo_links', 'option');
$SEOLinks          = get_field('footer_seo_links', 'option');        // The one under Brand Settings > Footer Settings > SEO Links

if (!empty($syncFooter) && !empty($settingsOverAPI['footerLinks'])) : ?>
    <div class="footer-links container">
        <div class="footer-links__columns">
        <?php
        foreach ($settingsOverAPI['footerLinks'] as $linkGroup) : ?>
            <div class="footer-links__column">
                <h4 class="footer-links__title"><?= $linkGroup['title'] ?></h4>
                <ul>
                    <?php foreach ($linkGroup['items'] as $link) : ?>
                        <?php if (!empty($link['text'])) : ?>
                            <li><a href="<?= $link['url'] ?>"><?= $link['text'] ?></a></li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endforeach; ?>
        </div>
    </div>
<?php elseif ($IsSEOLinksEnabled && !empty($SEOLinks)) : ?>
    <div class="footer-links">
        <div class="footer-links__columns">
        <?php foreach ($SEOLinks as $column) : ?>
            <div class="footer-links__column">
                <h4 class="footer-links__title"><?= $column['title'] ?></h4>
                <ul>
                    <?php foreach ($column['links'] as $link) : ?>
                        <li><a href="<?= $link['link_url'] ?>"><?= $link['link_text'] ?></a></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endforeach; ?>
        </div>
    </div>
<?php endif; ?>