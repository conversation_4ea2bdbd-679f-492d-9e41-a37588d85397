# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-05-22T11:47:45+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: pt_AO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/Blocks/Bindings.php:35
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr ""

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn how to fix this"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr ""

#: includes/api/api-template.php:376 includes/api/api-template.php:430
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""

#: includes/api/api-template.php:46 includes/api/api-template.php:242
#: includes/api/api-template.php:934
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#. %3$s - Link to show more details about the error
#: includes/admin/views/escaped-html-notice.php:19
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s. %3$s."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:14
msgid "Please contact your site administrator or developer for more details."
msgstr ""

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr ""

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:11
msgid "Show&nbsp;details"
msgstr ""

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:34
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr ""

#: includes/admin/views/global/navigation.php:223
msgid "Renew ACF PRO License"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr ""

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr ""

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr ""

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr ""

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:56
msgid "(Duplicated from %s)"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:117
#: assets/build/js/acf-field-group.js:2782
#: assets/build/js/acf-field-group.js:3272
msgid "This Field"
msgstr ""

#: includes/admin/admin.php:311
msgid "ACF PRO"
msgstr ""

#: includes/admin/admin.php:309
msgid "Feedback"
msgstr ""

#: includes/admin/admin.php:307
msgid "Support"
msgstr ""

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:282
msgid "is developed and maintained by"
msgstr ""

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr ""

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr ""

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr ""

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr ""

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:387
#: includes/fields/class-acf-field-select.php:386
#: includes/fields/class-acf-field-user.php:82
msgid "Select Multiple"
msgstr ""

#: includes/admin/views/global/navigation.php:235
msgid "WP Engine logo"
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1136
msgid "The capability name for assigning terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1135
msgid "Assign Terms Capability"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1119
msgid "The capability name for deleting terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1118
msgid "Delete Terms Capability"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1102
msgid "The capability name for editing terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1101
msgid "Edit Terms Capability"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1085
msgid "The capability name for managing terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1084
msgid "Manage Terms Capability"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:891
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr ""

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
msgid "Learn More"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr ""

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr ""

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr ""

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr ""

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr ""

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr ""

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:432
#: includes/fields/class-acf-field-post_object.php:350
#: includes/fields/class-acf-field-relationship.php:553
msgid "Any post status"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr ""

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr ""

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr ""

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr ""

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "We do not recommend using this field in ACF Blocks."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:23
msgid "WYSIWYG Editor"
msgstr ""

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr ""

#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:25
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""

#: includes/fields/class-acf-field-time_picker.php:25
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""

#: includes/fields/class-acf-field-textarea.php:24
msgid "A basic textarea input for storing paragraphs of text."
msgstr ""

#: includes/fields/class-acf-field-text.php:24
msgid "A basic text input, useful for storing single string values."
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""

#: includes/fields/class-acf-field-tab.php:26
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""

#: includes/fields/class-acf-field-select.php:25
msgid "A dropdown list with a selection of choices that you specify."
msgstr ""

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""

#: includes/fields/class-acf-field-range.php:24
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""

#: includes/fields/class-acf-field-radio.php:25
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""

#: includes/fields/class-acf-field-post_object.php:19
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""

#: includes/fields/class-acf-field-password.php:24
msgid "An input for providing a password using a masked field."
msgstr ""

#: includes/fields/class-acf-field-page_link.php:424
#: includes/fields/class-acf-field-post_object.php:342
#: includes/fields/class-acf-field-relationship.php:545
msgid "Filter by Post Status"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:25
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""

#: includes/fields/class-acf-field-oembed.php:25
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""

#: includes/fields/class-acf-field-number.php:24
msgid "An input limited to numerical values."
msgstr ""

#: includes/fields/class-acf-field-message.php:26
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""

#: includes/fields/class-acf-field-link.php:25
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""

#: includes/fields/class-acf-field-image.php:25
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""

#: includes/fields/class-acf-field-group.php:25
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""

#: includes/fields/class-acf-field-google-map.php:25
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""

#: includes/fields/class-acf-field-file.php:25
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""

#: includes/fields/class-acf-field-email.php:24
msgid "A text input specifically designed for storing email addresses."
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:25
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:25
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:25
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""

#: includes/fields/class-acf-field-button-group.php:26
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:27
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""

#: includes/fields.php:456
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""

#: includes/fields.php:446
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""

#: includes/fields.php:436
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""

#: includes/fields.php:426
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""

#: includes/fields.php:423
msgctxt "noun"
msgid "Clone"
msgstr ""

#: includes/admin/views/global/navigation.php:86 includes/fields.php:338
msgid "PRO"
msgstr ""

#: includes/fields.php:336 includes/fields.php:393
msgid "Advanced"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:85
msgid "JSON (newer)"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Original"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:55
msgid "Invalid post ID."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid post type selected for review."
msgstr ""

#: includes/admin/views/global/navigation.php:186
msgid "More"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr ""

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr ""

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
msgid "No search results for '%s'"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr ""

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Expose this post type in the REST API."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1055
msgid "Customize the query variable name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1028
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid "Customize the slug used in the URL"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:924
msgid "Permalinks for this taxonomy are disabled."
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:921
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:913
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1030
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:911
msgid "Select the type of permalink to use for this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:896
msgid "Display a column for the taxonomy on post type listing screens."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:895
msgid "Show Admin Column"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:882
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:881
msgid "Quick Edit"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:868
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:867
msgid "Tag Cloud"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:824
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:823
msgid "Meta Box Sanitization Callback"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:805
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid "Register Meta Box Callback"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:763
msgid "No Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:762
msgid "Custom Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:758
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:757
msgid "Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Categories Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:766
msgid "Tags Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Controller Class"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1256
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "The namespace part of the REST API URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1255
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1190
msgid "Namespace Route"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1172
msgid "The base URL for the post type REST API URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1236
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1171
msgid "Base URL"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1222
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1154
msgid "Show In REST API"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1200
msgid "Customize the query variable name."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1199
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1054
msgid "Query Variable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1177
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1032
msgid "No Query Variable Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1176
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1031
msgid "Custom Query Variable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1173
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1172
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1027
msgid "Query Variable Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1147
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1003
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1146
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1002
msgid "Publicly Queryable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1125
msgid "Custom slug for the Archive URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1124
msgid "Archive Slug"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1111
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "Archive"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
msgid "Pagination support for the items URLs such as the archives."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1089
msgid "Pagination"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1072
msgid "RSS feed URL for the post type items."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Feed URL"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1052
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "Front URL Prefix"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1033
msgid "Customize the slug used in the URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1032
#: includes/admin/views/acf-taxonomy/advanced-settings.php:940
msgid "URL Slug"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1016
msgid "Permalinks for this post type are disabled."
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1015
#: includes/admin/views/acf-taxonomy/advanced-settings.php:923
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1007
#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "No Permalink (prevent URL rewriting)"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1006
#: includes/admin/views/acf-taxonomy/advanced-settings.php:914
msgid "Custom Permalink"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1005
#: includes/admin/views/acf-post-type/advanced-settings.php:1175
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1003
#: includes/admin/views/acf-post-type/advanced-settings.php:1013
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1001
#: includes/admin/views/acf-taxonomy/advanced-settings.php:910
msgid "Permalink Rewrite"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:987
msgid "Delete items by a user when that user is deleted."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:986
msgid "Delete With User"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:972
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:971
msgid "Can Export"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:940
msgid "Optionally provide a plural to be used in capabilities."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:939
msgid "Plural Capability Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:921
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:920
msgid "Singular Capability Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:906
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:905
msgid "Rename Capabilities"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:890
msgid "Exclude From Search"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:877
#: includes/admin/views/acf-taxonomy/advanced-settings.php:854
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:876
#: includes/admin/views/acf-taxonomy/advanced-settings.php:853
msgid "Appearance Menus Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:858
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:857
msgid "Show In Admin Bar"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:826
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:825
msgid "Custom Meta Box Callback"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:805
msgid "Menu Icon"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:787
msgid "The position in the sidebar menu in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:786
msgid "Menu Position"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:768
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:767
msgid "Admin Menu Parent"
msgstr ""

#. translators: %s = "dashicon class name", link to the WordPress dashicon
#. documentation.
#: includes/admin/views/acf-post-type/advanced-settings.php:755
msgid ""
"The icon used for the post type menu item in the admin dashboard. Can be a "
"URL or %s to use for the icon."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:750
msgid "Dashicon class name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:289
msgid "Nothing to import"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:284
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ""

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:275
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/tools/class-acf-admin-tool-import.php:259
msgid "Failed to import taxonomies."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:241
msgid "Failed to import post types."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:230
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:206
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/tools/class-acf-admin-tool-import.php:121
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:110
#: includes/admin/tools/class-acf-admin-tool-import.php:126
msgid "Import from Custom Post Type UI"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's functions."
"php file or include it within an external file, then deactivate or delete "
"the items from the ACF admin."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-taxonomy.php:127
#: assets/build/js/acf-internal-post-type.js:182
#: assets/build/js/acf-internal-post-type.js:256
msgid "Category"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:125
#: assets/build/js/acf-internal-post-type.js:179
#: assets/build/js/acf-internal-post-type.js:253
msgid "Tag"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr ""

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:81
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:126
#: assets/build/js/acf-internal-post-type.js:176
#: assets/build/js/acf-internal-post-type.js:250
msgid "Pages"
msgstr ""

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr ""

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr ""

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:116
#: assets/build/js/acf-field-group.js:1146
#: assets/build/js/acf-field-group.js:1367
msgid "Type to search..."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:101
#: assets/build/js/acf-field-group.js:1173
#: assets/build/js/acf-field-group.js:2336
#: assets/build/js/acf-field-group.js:1413
#: assets/build/js/acf-field-group.js:2745
msgid "PRO Only"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:93
#: assets/build/js/acf-internal-post-type.js:308
#: assets/build/js/acf-internal-post-type.js:417
msgid "Field groups linked successfully."
msgstr ""

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""

#: includes/admin/admin.php:45 includes/admin/admin.php:311
msgid "ACF"
msgstr ""

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr ""

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr ""

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr ""

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr ""

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr ""

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr ""

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr ""

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr ""

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr ""

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr ""

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:249
msgid "Field Settings Tabs"
msgstr ""

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?utm_source=wordpress."
"org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""

#: includes/api/api-template.php:1010
msgid "[ACF shortcode value disabled for preview]"
msgstr ""

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:539
msgid "Close Modal"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:92
#: assets/build/js/acf-field-group.js:1688
#: assets/build/js/acf-field-group.js:2016
msgid "Field moved to other group"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:91
#: assets/build/js/acf.js:1437 assets/build/js/acf.js:1517
msgid "Close modal"
msgstr ""

#: includes/fields/class-acf-field-tab.php:118
msgid "Start a new group of tabs at this tab."
msgstr ""

#: includes/fields/class-acf-field-tab.php:117
msgid "New Tab Group"
msgstr ""

#: includes/fields/class-acf-field-select.php:429
#: includes/fields/class-acf-field-true_false.php:190
msgid "Use a stylized checkbox using select2"
msgstr ""

#: includes/fields/class-acf-field-radio.php:253
msgid "Save Other Choice"
msgstr ""

#: includes/fields/class-acf-field-radio.php:242
msgid "Allow Other Choice"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:425
msgid "Add Toggle All"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:384
msgid "Save Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:373
msgid "Allow Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:137
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""

#: includes/admin/views/global/navigation.php:250
msgid "Updates"
msgstr ""

#: includes/admin/views/global/navigation.php:176
msgid "Advanced Custom Fields logo"
msgstr ""

#: includes/admin/views/global/form-top.php:89
msgid "Save Changes"
msgstr ""

#: includes/admin/views/global/form-top.php:76
msgid "Field Group Title"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr ""

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr ""

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr ""

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:252
msgid "Options Pages"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr ""

#: includes/admin/views/global/navigation.php:212
msgid "Unlock Extra Features with ACF PRO"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr ""

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr ""

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr ""

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr ""

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr ""

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:85
msgid "Add Field"
msgstr ""

#: includes/acf-field-group-functions.php:496 includes/fields.php:391
msgid "Presentation"
msgstr ""

#: includes/fields.php:390
msgid "Validation"
msgstr ""

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:389
msgid "General"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:69
msgid "Import JSON"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr ""

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-internal-post-type-list.php:468
#: includes/admin/admin-internal-post-type-list.php:494
msgid "Deactivate"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:468
msgid "Deactivate this item"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:464
#: includes/admin/admin-internal-post-type-list.php:493
msgid "Activate"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:464
msgid "Activate this item"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:88
#: assets/build/js/acf-field-group.js:2841
#: assets/build/js/acf-field-group.js:3349
msgid "Move field group to trash?"
msgstr ""

#: acf.php:490 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:274
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr ""

#. Author of the plugin
#: acf.php
msgid "WP Engine"
msgstr ""

#: acf.php:548
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""

#: acf.php:546
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""

#. translators: %1 plugin name, %2 the URL to the documentation on this error
#: includes/acf-value-functions.php:376
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" "
"target=\"_blank\">Learn how to fix this</a>."
msgstr ""

#: includes/fields/class-acf-field-user.php:549
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] ""
msgstr[1] ""

#: includes/fields/class-acf-field-user.php:540
msgid "%1$s must have a valid user ID."
msgstr ""

#: includes/fields/class-acf-field-user.php:379
msgid "Invalid request."
msgstr ""

#: includes/fields/class-acf-field-select.php:646
msgid "%1$s is not one of %2$s"
msgstr ""

#: includes/fields/class-acf-field-post_object.php:645
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] ""
msgstr[1] ""

#: includes/fields/class-acf-field-post_object.php:629
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] ""
msgstr[1] ""

#: includes/fields/class-acf-field-post_object.php:620
msgid "%1$s must have a valid post ID."
msgstr ""

#: includes/fields/class-acf-field-file.php:453
msgid "%s requires a valid attachment ID."
msgstr ""

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:160
msgid "Enable Transparency"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:179
msgid "RGBA Array"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:94
msgid "RGBA String"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:93
#: includes/fields/class-acf-field-color_picker.php:178
msgid "Hex String"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:274
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr ""

#: includes/fields/class-acf-field-email.php:168
msgid "'%s' is not a valid email address"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:72
msgid "Color value"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select default color"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear color"
msgstr ""

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr ""

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr ""

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr ""

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr ""

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr ""

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr ""

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:92
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr ""

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
#: assets/build/js/acf-internal-post-type.js:173
#: assets/build/js/acf-internal-post-type.js:247
msgid "Posts"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:76
msgid "Last updated: %s"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:70
msgid "Sorry, this post is unavailable for diff comparison."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:42
msgid "Invalid field group parameter(s)."
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:48
msgid "Import"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr ""

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:501
msgid "Sync changes"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr ""

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr ""

#: includes/admin/admin.php:173
msgid "View details"
msgstr ""

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr ""

#: includes/admin/admin.php:171
msgid "Information"
msgstr ""

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr ""

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr ""

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr ""

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr ""

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr ""

#: includes/fields/class-acf-field-user.php:374
msgid "Error loading field."
msgstr ""

#: assets/build/js/acf-input.js:2748 assets/build/js/acf-input.js:2817
#: assets/build/js/acf-input.js:2926 assets/build/js/acf-input.js:3000
msgid "Location not found: %s"
msgstr ""

#: includes/forms/form-user.php:337
msgid "<strong>Error</strong>: %s"
msgstr ""

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Papel de utilizador"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Comentário"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Formato de artigo"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Item de menu"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Estado do conteúdo"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menus"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Localizações do menu"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Taxonomia do artigo"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Página dependente (tem superior)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Página superior (tem dependentes)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Página de topo (sem superior)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Página de artigos"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Página inicial"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Tipo de página"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "A visualizar a administração do site"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "A visualizar a frente do site"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Sessão iniciada"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Utilizador actual"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Modelo de página"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registar"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Adicionar / Editar"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Formulário de utilizador"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Página superior"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super Administrador"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Papel do utilizador actual"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Modelo por omissão"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Modelo de conteúdo"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Categoria de artigo"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Todos os formatos de %s"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Anexo"

#: includes/validation.php:319
msgid "%s value is required"
msgstr "O valor %s é obrigatório"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Mostrar este campo se"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:392
msgid "Conditional Logic"
msgstr "Lógica condicional"

#: includes/admin/views/acf-field-group/conditional-logic.php:161
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "e"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "JSON local"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Campo de clone"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Por favor, verifique se todos os add-ons premium (%s) estão actualizados "
"para a última versão."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Esta versão inclui melhorias na base de dados e requer uma actualização."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr ""

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Actualização da base de dados necessária"

#: includes/admin/post-types/admin-field-group.php:129
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Página de opções"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:443
msgid "Gallery"
msgstr "Galeria"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:433
msgid "Flexible Content"
msgstr "Conteúdo flexível"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:453
msgid "Repeater"
msgstr "Repetidor"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Voltar para todas as ferramentas"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Se forem mostrados vários grupos de campos num ecrã de edição, serão "
"utilizadas as opções do primeiro grupo de campos. (o que tiver menor número "
"de ordem)"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Seleccione</b> os itens a <b>esconder</b> do ecrã de edição."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Esconder no ecrã"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Enviar trackbacks"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
#: assets/build/js/acf-internal-post-type.js:180
#: assets/build/js/acf-internal-post-type.js:254
msgid "Tags"
msgstr "Etiquetas"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
#: assets/build/js/acf-internal-post-type.js:183
#: assets/build/js/acf-internal-post-type.js:257
msgid "Categories"
msgstr "Categorias"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Atributos da página"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Formato"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Autor"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Revisões"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Comentários"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Discussão"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Excerto"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Editor de conteúdo"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Ligação permanente"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Mostrado na lista de grupos de campos"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr ""
"Serão mostrados primeiro os grupos de campos com menor número de ordem."

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "Nº. de ordem"

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Abaixo dos campos"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Abaixo das legendas"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Lateral"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Normal (depois do conteúdo)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "Acima (depois do título)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Posição"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Simples (sem metabox)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Predefinido (metabox do WP)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Estilo"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Tipo"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Chave"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Ordem"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Fechar campo"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "classe"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "largura"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Atributos do wrapper"

#: includes/fields/class-acf-field.php:311
msgid "Required"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for authors. Shown when submitting data"
msgstr ""
"Instruções para os autores. São mostradas ao preencher e submeter dados."

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Instruções"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Tipo de campo"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr ""
"Uma única palavra, sem espaços. São permitidos underscores (_) e traços (-)."

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Nome do campo"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Este é o nome que será mostrado na página EDITAR."

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Legenda do campo"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Eliminar"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Eliminar campo"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Mover"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Mover campo para outro grupo"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Duplicar campo"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Editar campo"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Arraste para reordenar"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
#: assets/build/js/acf-field-group.js:2374
#: assets/build/js/acf-field-group.js:2796
msgid "Show this field group if"
msgstr "Mostrar este grupo de campos se"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Nenhuma actualização disponível."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Actualização da base de dados concluída. <a href=\"%s\">Ver o que há de "
"novo</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "A ler tarefas de actualização..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Falhou ao actualizar."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Actualização concluída."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "A actualizar dados para a versão %s"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"É recomendável que faça uma cópia de segurança da sua base de dados antes de "
"continuar. Tem a certeza que quer actualizar agora?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Por favor, seleccione pelo menos um site para actualizar."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Actualização da base de dados concluída. <a href=\"%s\">Voltar ao painel da "
"rede</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "O site está actualizado"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr ""

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Site"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Actualizar sites"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Os sites seguintes necessitam de actualização da BD. Seleccione os que quer "
"actualizar e clique em %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:176
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Adicionar grupo de regras"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Crie um conjunto de regras para determinar em que ecrãs de edição serão "
"utilizados estes campos personalizados avançados"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Regras"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Copiado"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Copiar para a área de transferência"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Seleccione os grupos de campos"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "Nenhum grupo de campos seleccionado"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "Gerar PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Exportar grupos de campos"

#: includes/admin/tools/class-acf-admin-tool-import.php:174
msgid "Import file empty"
msgstr "Ficheiro de importação vazio"

#: includes/admin/tools/class-acf-admin-tool-import.php:165
msgid "Incorrect file type"
msgstr "Tipo de ficheiro incorrecto"

#: includes/admin/tools/class-acf-admin-tool-import.php:160
msgid "Error uploading file. Please try again"
msgstr "Erro ao carregar ficheiro. Por favor tente de novo."

#: includes/admin/tools/class-acf-admin-tool-import.php:49
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:27
msgid "Import Field Groups"
msgstr "Importar grupos de campos"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Sincronizar"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:885
msgid "Select %s"
msgstr "Seleccionar %s"

#: includes/admin/admin-internal-post-type-list.php:458
#: includes/admin/admin-internal-post-type-list.php:490
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Duplicar"

#: includes/admin/admin-internal-post-type-list.php:458
msgid "Duplicate this item"
msgstr "Duplicar este item"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr ""

#: includes/admin/admin.php:305
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Documentação"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Descrição"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:757
msgid "Sync available"
msgstr "Sincronização disponível"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Grupo de campos duplicado."
msgstr[1] "%s grupos de campos duplicados."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Activo <span class=\"count\">(%s)</span>"
msgstr[1] "Activos <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Rever sites e actualizar"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Actualizar base de dados"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Campos personalizados"

#: includes/admin/post-types/admin-field-group.php:584
msgid "Move Field"
msgstr "Mover campo"

#: includes/admin/post-types/admin-field-group.php:573
#: includes/admin/post-types/admin-field-group.php:577
msgid "Please select the destination for this field"
msgstr "Por favor seleccione o destinho para este campo"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:535
msgid "The %1$s field can now be found in the %2$s field group"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:532
msgid "Move Complete."
msgstr "Movido com sucesso."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Activo"

#: includes/admin/post-types/admin-field-group.php:246
msgid "Field Keys"
msgstr "Chaves dos campos"

#: includes/admin/post-types/admin-field-group.php:150
msgid "Settings"
msgstr "Definições"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "Localização"

#: includes/admin/post-types/admin-field-group.php:100
#: assets/build/js/acf-input.js:983 assets/build/js/acf-input.js:1075
msgid "Null"
msgstr "Nulo"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
#: assets/build/js/acf-field-group.js:1528
#: assets/build/js/acf-field-group.js:1844
msgid "copy"
msgstr "cópia"

#: includes/admin/post-types/admin-field-group.php:96
#: assets/build/js/acf-field-group.js:624
#: assets/build/js/acf-field-group.js:779
msgid "(this field)"
msgstr "(este campo)"

#: includes/admin/post-types/admin-field-group.php:94
#: assets/build/js/acf-input.js:918 assets/build/js/acf-input.js:943
#: assets/build/js/acf-input.js:1002 assets/build/js/acf-input.js:1030
msgid "Checked"
msgstr "Seleccionado"

#: includes/admin/post-types/admin-field-group.php:90
#: assets/build/js/acf-field-group.js:1633
#: assets/build/js/acf-field-group.js:1956
msgid "Move Custom Field"
msgstr "Mover campo personalizado"

#: includes/admin/post-types/admin-field-group.php:89
#: assets/build/js/acf-field-group.js:650
#: assets/build/js/acf-field-group.js:805
msgid "No toggle fields available"
msgstr "Nenhum campo de opções disponível"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "O título do grupo de campos é obrigatório"

#: includes/admin/post-types/admin-field-group.php:86
#: assets/build/js/acf-field-group.js:1622
#: assets/build/js/acf-field-group.js:1942
msgid "This field cannot be moved until its changes have been saved"
msgstr ""
"Este campo não pode ser movido até que as suas alterações sejam guardadas."

#: includes/admin/post-types/admin-field-group.php:85
#: assets/build/js/acf-field-group.js:1432
#: assets/build/js/acf-field-group.js:1739
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr ""
"O prefixo \"field_\" não pode ser utilizado no início do nome do campo."

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Rascunho de grupo de campos actualizado."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Grupo de campos agendado."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Grupo de campos enviado."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Grupo de campos guardado."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Grupo de campos publicado."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Grupo de campos eliminado."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Grupo de campos actualizado."

#: includes/admin/admin-tools.php:112
#: includes/admin/views/global/navigation.php:248
#: includes/admin/views/tools/tools.php:13
msgid "Tools"
msgstr "Ferramentas"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "não é igual a"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "é igual a"

#: includes/locations.php:104
msgid "Forms"
msgstr "Formulários"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
#: assets/build/js/acf-internal-post-type.js:175
#: assets/build/js/acf-internal-post-type.js:249
msgid "Page"
msgstr "Página"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
#: assets/build/js/acf-internal-post-type.js:172
#: assets/build/js/acf-internal-post-type.js:246
msgid "Post"
msgstr "Artigo"

#: includes/fields.php:335
msgid "Relational"
msgstr "Relacional"

#: includes/fields.php:334
msgid "Choice"
msgstr "Opção"

#: includes/fields.php:332
msgid "Basic"
msgstr "Básico"

#: includes/fields.php:283
msgid "Unknown"
msgstr "Desconhecido"

#: includes/fields.php:283
msgid "Field type does not exist"
msgstr "Tipo de campo não existe"

#: includes/forms/form-front.php:219
msgid "Spam Detected"
msgstr "Spam detectado"

#: includes/forms/form-front.php:102
msgid "Post updated"
msgstr "Artigo actualizado"

#: includes/forms/form-front.php:101
msgid "Update"
msgstr "Actualizar"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Validar email"

#: includes/fields.php:333 includes/forms/form-front.php:47
msgid "Content"
msgstr "Conteúdo"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:38
msgid "Title"
msgstr "Título"

#: includes/assets.php:373 includes/forms/form-comment.php:144
#: assets/build/js/acf-input.js:7395 assets/build/js/acf-input.js:7984
msgid "Edit field group"
msgstr "Editar grupo de campos"

#: includes/admin/post-types/admin-field-group.php:113
#: assets/build/js/acf-input.js:1125 assets/build/js/acf-input.js:1230
msgid "Selection is less than"
msgstr "A selecção é menor do que"

#: includes/admin/post-types/admin-field-group.php:112
#: assets/build/js/acf-input.js:1106 assets/build/js/acf-input.js:1202
msgid "Selection is greater than"
msgstr "A selecção é maior do que"

#: includes/admin/post-types/admin-field-group.php:111
#: assets/build/js/acf-input.js:1075 assets/build/js/acf-input.js:1170
msgid "Value is less than"
msgstr "O valor é menor do que"

#: includes/admin/post-types/admin-field-group.php:110
#: assets/build/js/acf-input.js:1045 assets/build/js/acf-input.js:1139
msgid "Value is greater than"
msgstr "O valor é maior do que"

#: includes/admin/post-types/admin-field-group.php:109
#: assets/build/js/acf-input.js:888 assets/build/js/acf-input.js:960
msgid "Value contains"
msgstr "O valor contém"

#: includes/admin/post-types/admin-field-group.php:108
#: assets/build/js/acf-input.js:862 assets/build/js/acf-input.js:926
msgid "Value matches pattern"
msgstr "O valor corresponde ao padrão"

#: includes/admin/post-types/admin-field-group.php:107
#: assets/build/js/acf-input.js:840 assets/build/js/acf-input.js:1023
#: assets/build/js/acf-input.js:903 assets/build/js/acf-input.js:1116
msgid "Value is not equal to"
msgstr "O valor é diferente de"

#: includes/admin/post-types/admin-field-group.php:106
#: assets/build/js/acf-input.js:810 assets/build/js/acf-input.js:964
#: assets/build/js/acf-input.js:864 assets/build/js/acf-input.js:1053
msgid "Value is equal to"
msgstr "O valor é igual a"

#: includes/admin/post-types/admin-field-group.php:105
#: assets/build/js/acf-input.js:788 assets/build/js/acf-input.js:841
msgid "Has no value"
msgstr "Não tem valor"

#: includes/admin/post-types/admin-field-group.php:104
#: assets/build/js/acf-input.js:758 assets/build/js/acf-input.js:783
msgid "Has any value"
msgstr "Tem um valor qualquer"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
#: assets/build/js/acf.js:1564 assets/build/js/acf.js:1658
msgid "Cancel"
msgstr "Cancelar"

#: includes/assets.php:350 assets/build/js/acf.js:1738
#: assets/build/js/acf.js:1855
msgid "Are you sure?"
msgstr "Tem a certeza?"

#: includes/assets.php:370 assets/build/js/acf-input.js:9464
#: assets/build/js/acf-input.js:10331
msgid "%d fields require attention"
msgstr "%d campos requerem a sua atenção"

#: includes/assets.php:369 assets/build/js/acf-input.js:9462
#: assets/build/js/acf-input.js:10327
msgid "1 field requires attention"
msgstr "1 campo requer a sua atenção"

#: includes/assets.php:368 includes/validation.php:253
#: includes/validation.php:261 assets/build/js/acf-input.js:9457
#: assets/build/js/acf-input.js:10322
msgid "Validation failed"
msgstr "A validação falhou"

#: includes/assets.php:367 assets/build/js/acf-input.js:9625
#: assets/build/js/acf-input.js:10510
msgid "Validation successful"
msgstr "Validação bem sucedida"

#: includes/media.php:54 assets/build/js/acf-input.js:7223
#: assets/build/js/acf-input.js:7788
msgid "Restricted"
msgstr "Restrito"

#: includes/media.php:53 assets/build/js/acf-input.js:7038
#: assets/build/js/acf-input.js:7552
msgid "Collapse Details"
msgstr "Minimizar detalhes"

#: includes/media.php:52 assets/build/js/acf-input.js:7038
#: assets/build/js/acf-input.js:7549
msgid "Expand Details"
msgstr "Expandir detalhes"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51 assets/build/js/acf-input.js:6905
#: assets/build/js/acf-input.js:7397
msgid "Uploaded to this post"
msgstr "Carregados neste artigo"

#: includes/media.php:50 assets/build/js/acf-input.js:6944
#: assets/build/js/acf-input.js:7436
msgctxt "verb"
msgid "Update"
msgstr "Actualizar"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Editar"

#: includes/assets.php:364 assets/build/js/acf-input.js:9234
#: assets/build/js/acf-input.js:10093
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"As alterações que fez serão ignoradas se navegar para fora desta página."

#: includes/api/api-helpers.php:2950
msgid "File type must be %s."
msgstr "O tipo de ficheiro deve ser %s."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:174
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2947 assets/build/js/acf-field-group.js:772
#: assets/build/js/acf-field-group.js:2414
#: assets/build/js/acf-field-group.js:934
#: assets/build/js/acf-field-group.js:2843
msgid "or"
msgstr "ou"

#: includes/api/api-helpers.php:2923
msgid "File size must not exceed %s."
msgstr ""

#: includes/api/api-helpers.php:2919
msgid "File size must be at least %s."
msgstr "O tamanho do ficheiro deve ser pelo menos de %s."

#: includes/api/api-helpers.php:2906
msgid "Image height must not exceed %dpx."
msgstr "A altura da imagem não deve exceder os %dpx."

#: includes/api/api-helpers.php:2902
msgid "Image height must be at least %dpx."
msgstr "A altura da imagem deve ser pelo menos de %dpx."

#: includes/api/api-helpers.php:2890
msgid "Image width must not exceed %dpx."
msgstr "A largura da imagem não deve exceder os %dpx."

#: includes/api/api-helpers.php:2886
msgid "Image width must be at least %dpx."
msgstr "A largura da imagem deve ser pelo menos de %dpx."

#: includes/api/api-helpers.php:1400 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(sem título)"

#: includes/api/api-helpers.php:760
msgid "Full Size"
msgstr "Tamanho original"

#: includes/api/api-helpers.php:725
msgid "Large"
msgstr "Grande"

#: includes/api/api-helpers.php:724
msgid "Medium"
msgstr "Média"

#: includes/api/api-helpers.php:723
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
#: assets/build/js/acf-field-group.js:1077
#: assets/build/js/acf-field-group.js:1261
msgid "(no label)"
msgstr "(sem legenda)"

#: includes/fields/class-acf-field-textarea.php:137
msgid "Sets the textarea height"
msgstr "Define a altura da área de texto"

#: includes/fields/class-acf-field-textarea.php:136
msgid "Rows"
msgstr "Linhas"

#: includes/fields/class-acf-field-textarea.php:23
msgid "Text Area"
msgstr "Área de texto"

#: includes/fields/class-acf-field-checkbox.php:426
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"Preceder com caixa de selecção adicional para seleccionar todas as opções"

#: includes/fields/class-acf-field-checkbox.php:388
msgid "Save 'custom' values to the field's choices"
msgstr "Guarda valores personalizados nas opções do campo"

#: includes/fields/class-acf-field-checkbox.php:377
msgid "Allow 'custom' values to be added"
msgstr "Permite adicionar valores personalizados"

#: includes/fields/class-acf-field-checkbox.php:36
msgid "Add new choice"
msgstr "Adicionar nova opção"

#: includes/fields/class-acf-field-checkbox.php:161
msgid "Toggle All"
msgstr "Seleccionar tudo"

#: includes/fields/class-acf-field-page_link.php:454
msgid "Allow Archives URLs"
msgstr "Permitir URL do arquivo"

#: includes/fields/class-acf-field-page_link.php:163
msgid "Archives"
msgstr "Arquivo"

#: includes/fields/class-acf-field-page_link.php:23
msgid "Page Link"
msgstr "Ligação de página"

#: includes/fields/class-acf-field-taxonomy.php:873
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Adicionar"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:843
msgid "Name"
msgstr "Nome"

#: includes/fields/class-acf-field-taxonomy.php:828
msgid "%s added"
msgstr "%s adicionado(a)"

#: includes/fields/class-acf-field-taxonomy.php:792
msgid "%s already exists"
msgstr "%s já existe"

#: includes/fields/class-acf-field-taxonomy.php:780
msgid "User unable to add new %s"
msgstr "O utilizador não pôde adicionar novo(a) %s"

#: includes/fields/class-acf-field-taxonomy.php:680
msgid "Term ID"
msgstr "ID do termo"

#: includes/fields/class-acf-field-taxonomy.php:679
msgid "Term Object"
msgstr "Termo"

#: includes/fields/class-acf-field-taxonomy.php:664
msgid "Load value from posts terms"
msgstr "Carrega os termos a partir dos termos dos conteúdos."

#: includes/fields/class-acf-field-taxonomy.php:663
msgid "Load Terms"
msgstr "Carregar termos"

#: includes/fields/class-acf-field-taxonomy.php:653
msgid "Connect selected terms to the post"
msgstr "Liga os termos seleccionados ao conteúdo."

#: includes/fields/class-acf-field-taxonomy.php:652
msgid "Save Terms"
msgstr "Guardar termos"

#: includes/fields/class-acf-field-taxonomy.php:642
msgid "Allow new terms to be created whilst editing"
msgstr "Permite a criação de novos termos durante a edição."

#: includes/fields/class-acf-field-taxonomy.php:641
msgid "Create Terms"
msgstr "Criar termos"

#: includes/fields/class-acf-field-taxonomy.php:700
msgid "Radio Buttons"
msgstr "Botões de opções"

#: includes/fields/class-acf-field-taxonomy.php:699
msgid "Single Value"
msgstr "Valor único"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Multi Select"
msgstr "Selecção múltipla"

#: includes/fields/class-acf-field-checkbox.php:23
#: includes/fields/class-acf-field-taxonomy.php:696
msgid "Checkbox"
msgstr "Caixa de selecção"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multiple Values"
msgstr "Valores múltiplos"

#: includes/fields/class-acf-field-taxonomy.php:690
msgid "Select the appearance of this field"
msgstr "Seleccione a apresentação deste campo."

#: includes/fields/class-acf-field-taxonomy.php:689
msgid "Appearance"
msgstr "Apresentação"

#: includes/fields/class-acf-field-taxonomy.php:631
msgid "Select the taxonomy to be displayed"
msgstr "Seleccione a taxonomia que será mostrada."

#: includes/fields/class-acf-field-taxonomy.php:595
msgctxt "No Terms"
msgid "No %s"
msgstr ""

#: includes/fields/class-acf-field-number.php:244
msgid "Value must be equal to or lower than %d"
msgstr "O valor deve ser igual ou inferior a %d"

#: includes/fields/class-acf-field-number.php:239
msgid "Value must be equal to or higher than %d"
msgstr "O valor deve ser igual ou superior a %d"

#: includes/fields/class-acf-field-number.php:227
msgid "Value must be a number"
msgstr "O valor deve ser um número"

#: includes/fields/class-acf-field-number.php:23
msgid "Number"
msgstr "Número"

#: includes/fields/class-acf-field-radio.php:257
msgid "Save 'other' values to the field's choices"
msgstr "Guardar 'outros' valores nas opções do campo"

#: includes/fields/class-acf-field-radio.php:246
msgid "Add 'other' choice to allow for custom values"
msgstr ""
"Adicionar opção 'outros' para permitir a inserção de valores personalizados"

#: includes/admin/views/global/navigation.php:196
msgid "Other"
msgstr "Outro"

#: includes/fields/class-acf-field-radio.php:23
msgid "Radio Button"
msgstr "Botão de opção"

#: includes/fields/class-acf-field-accordion.php:105
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Define o fim do acordeão anterior. Este item de acordeão não será visível."

#: includes/fields/class-acf-field-accordion.php:94
msgid "Allow this accordion to open without closing others."
msgstr "Permite abrir este item de acordeão sem fechar os restantes."

#: includes/fields/class-acf-field-accordion.php:93
msgid "Multi-Expand"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:83
msgid "Display this accordion as open on page load."
msgstr "Mostrar este item de acordeão aberto ao carregar a página."

#: includes/fields/class-acf-field-accordion.php:82
msgid "Open"
msgstr "Aberto"

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "Acordeão"

#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
msgid "Restrict which files can be uploaded"
msgstr "Restringe que ficheiros podem ser carregados."

#: includes/fields/class-acf-field-file.php:210
msgid "File ID"
msgstr "ID do ficheiro"

#: includes/fields/class-acf-field-file.php:209
msgid "File URL"
msgstr "URL do ficheiro"

#: includes/fields/class-acf-field-file.php:208
msgid "File Array"
msgstr "Array do ficheiro"

#: includes/fields/class-acf-field-file.php:179
msgid "Add File"
msgstr "Adicionar ficheiro"

#: includes/admin/tools/class-acf-admin-tool-import.php:153
#: includes/fields/class-acf-field-file.php:179
msgid "No file selected"
msgstr "Nenhum ficheiro seleccionado"

#: includes/fields/class-acf-field-file.php:143
msgid "File name"
msgstr "Nome do ficheiro"

#: includes/fields/class-acf-field-file.php:59
#: assets/build/js/acf-input.js:2472 assets/build/js/acf-input.js:2625
msgid "Update File"
msgstr "Actualizar ficheiro"

#: includes/fields/class-acf-field-file.php:58
#: assets/build/js/acf-input.js:2471 assets/build/js/acf-input.js:2624
msgid "Edit File"
msgstr "Editar ficheiro"

#: includes/admin/tools/class-acf-admin-tool-import.php:57
#: includes/fields/class-acf-field-file.php:57
#: assets/build/js/acf-input.js:2445 assets/build/js/acf-input.js:2597
msgid "Select File"
msgstr "Seleccionar ficheiro"

#: includes/fields/class-acf-field-file.php:23
msgid "File"
msgstr "Ficheiro"

#: includes/fields/class-acf-field-password.php:23
msgid "Password"
msgstr "Senha"

#: includes/fields/class-acf-field-select.php:371
msgid "Specify the value returned"
msgstr "Especifica o valor devolvido."

#: includes/fields/class-acf-field-select.php:439
msgid "Use AJAX to lazy load choices?"
msgstr "Utilizar AJAX para carregar opções?"

#: includes/fields/class-acf-field-checkbox.php:338
#: includes/fields/class-acf-field-select.php:360
msgid "Enter each default value on a new line"
msgstr "Insira cada valor por omissão numa linha separada"

#: includes/fields/class-acf-field-select.php:235 includes/media.php:48
#: assets/build/js/acf-input.js:6803 assets/build/js/acf-input.js:7282
msgctxt "verb"
msgid "Select"
msgstr "Seleccionar"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Falhou ao carregar"

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "A pesquisar&hellip;"

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "A carregar mais resultados&hellip;"

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Só pode seleccionar %d itens"

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Só pode seleccionar 1 item"

#: includes/fields/class-acf-field-select.php:106
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Por favor elimine %d caracteres"

#: includes/fields/class-acf-field-select.php:105
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Por favor elimine 1 caractere"

#: includes/fields/class-acf-field-select.php:104
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Por favor insira %d ou mais caracteres"

#: includes/fields/class-acf-field-select.php:103
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Por favor insira 1 ou mais caracteres"

#: includes/fields/class-acf-field-select.php:102
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Nenhuma correspondência encontrada"

#: includes/fields/class-acf-field-select.php:101
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d resultados encontrados, use as setas para cima ou baixo para navegar."

#: includes/fields/class-acf-field-select.php:100
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Um resultado encontrado, prima Enter para seleccioná-lo."

#: includes/fields/class-acf-field-select.php:23
#: includes/fields/class-acf-field-taxonomy.php:701
msgctxt "noun"
msgid "Select"
msgstr "Selecção"

#: includes/fields/class-acf-field-user.php:73
msgid "User ID"
msgstr "ID do utilizador"

#: includes/fields/class-acf-field-user.php:72
msgid "User Object"
msgstr "Objecto do utilizador"

#: includes/fields/class-acf-field-user.php:71
msgid "User Array"
msgstr "Array do utilizador"

#: includes/fields/class-acf-field-user.php:59
msgid "All user roles"
msgstr "Todos os papéis de utilizador"

#: includes/fields/class-acf-field-user.php:51
msgid "Filter by Role"
msgstr ""

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Utilizador"

#: includes/fields/class-acf-field-separator.php:23
msgid "Separator"
msgstr "Divisória"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Select Color"
msgstr "Seleccionar cor"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:69
#: assets/build/js/acf-internal-post-type.js:72
#: assets/build/js/acf-internal-post-type.js:86
msgid "Default"
msgstr "Por omissão"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Clear"
msgstr "Limpar"

#: includes/fields/class-acf-field-color_picker.php:23
msgid "Color Picker"
msgstr "Selecção de cor"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:83
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:79
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Seleccionar"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Concluído"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Agora"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Fuso horário"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsegundo"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milissegundo"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Segundo"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuto"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Escolha a hora"

#: includes/fields/class-acf-field-date_time_picker.php:23
msgid "Date Time Picker"
msgstr "Selecção de data e hora"

#: includes/fields/class-acf-field-accordion.php:104
msgid "Endpoint"
msgstr "Fim"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:108
msgid "Left aligned"
msgstr "Alinhado à esquerda"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:107
msgid "Top aligned"
msgstr "Alinhado acima"

#: includes/fields/class-acf-field-tab.php:103
msgid "Placement"
msgstr "Posição"

#: includes/fields/class-acf-field-tab.php:24
msgid "Tab"
msgstr "Separador"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "O valor deve ser um URL válido"

#: includes/fields/class-acf-field-link.php:155
msgid "Link URL"
msgstr "URL da ligação"

#: includes/fields/class-acf-field-link.php:154
msgid "Link Array"
msgstr "Array da ligação"

#: includes/fields/class-acf-field-link.php:126
msgid "Opens in a new window/tab"
msgstr "Abre numa nova janela/separador"

#: includes/fields/class-acf-field-link.php:121
msgid "Select Link"
msgstr "Seleccionar ligação"

#: includes/fields/class-acf-field-link.php:23
msgid "Link"
msgstr "Ligação"

#: includes/fields/class-acf-field-email.php:23
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-number.php:176
#: includes/fields/class-acf-field-range.php:209
msgid "Step Size"
msgstr "Valor dos passos"

#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-range.php:187
msgid "Maximum Value"
msgstr "Valor máximo"

#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-range.php:176
msgid "Minimum Value"
msgstr "Valor mínimo"

#: includes/fields/class-acf-field-range.php:23
msgid "Range"
msgstr "Intervalo"

#: includes/fields/class-acf-field-button-group.php:166
#: includes/fields/class-acf-field-checkbox.php:355
#: includes/fields/class-acf-field-radio.php:213
#: includes/fields/class-acf-field-select.php:378
msgid "Both (Array)"
msgstr "Ambos (Array)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:354
#: includes/fields/class-acf-field-radio.php:212
#: includes/fields/class-acf-field-select.php:377
msgid "Label"
msgstr "Legenda"

#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:353
#: includes/fields/class-acf-field-radio.php:211
#: includes/fields/class-acf-field-select.php:376
msgid "Value"
msgstr "Valor"

#: includes/fields/class-acf-field-button-group.php:212
#: includes/fields/class-acf-field-checkbox.php:416
#: includes/fields/class-acf-field-radio.php:285
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:417
#: includes/fields/class-acf-field-radio.php:286
msgid "Horizontal"
msgstr "Horizontal"

#: includes/fields/class-acf-field-button-group.php:139
#: includes/fields/class-acf-field-checkbox.php:328
#: includes/fields/class-acf-field-radio.php:186
#: includes/fields/class-acf-field-select.php:349
msgid "red : Red"
msgstr "vermelho : Vermelho"

#: includes/fields/class-acf-field-button-group.php:139
#: includes/fields/class-acf-field-checkbox.php:328
#: includes/fields/class-acf-field-radio.php:186
#: includes/fields/class-acf-field-select.php:349
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Para maior controlo, pode especificar tanto os valores como as legendas:"

#: includes/fields/class-acf-field-button-group.php:139
#: includes/fields/class-acf-field-checkbox.php:328
#: includes/fields/class-acf-field-radio.php:186
#: includes/fields/class-acf-field-select.php:349
msgid "Enter each choice on a new line."
msgstr "Insira cada opção numa linha separada."

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:327
#: includes/fields/class-acf-field-radio.php:185
#: includes/fields/class-acf-field-select.php:348
msgid "Choices"
msgstr "Opções"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Grupo de botões"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-page_link.php:486
#: includes/fields/class-acf-field-post_object.php:408
#: includes/fields/class-acf-field-radio.php:231
#: includes/fields/class-acf-field-select.php:407
#: includes/fields/class-acf-field-taxonomy.php:710
#: includes/fields/class-acf-field-user.php:103
msgid "Allow Null"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:236
#: includes/fields/class-acf-field-post_object.php:230
#: includes/fields/class-acf-field-taxonomy.php:861
msgid "Parent"
msgstr "Superior"

#: includes/fields/class-acf-field-wysiwyg.php:371
msgid "TinyMCE will not be initialized until field is clicked"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:370
msgid "Delay Initialization"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:359
msgid "Show Media Upload Buttons"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:343
msgid "Toolbar"
msgstr "Barra de ferramentas"

#: includes/fields/class-acf-field-wysiwyg.php:335
msgid "Text Only"
msgstr "Apenas HTML"

#: includes/fields/class-acf-field-wysiwyg.php:334
msgid "Visual Only"
msgstr "Apenas visual"

#: includes/fields/class-acf-field-wysiwyg.php:333
msgid "Visual & Text"
msgstr "Visual e HTML"

#: includes/fields/class-acf-field-wysiwyg.php:328
msgid "Tabs"
msgstr "Separadores"

#: includes/fields/class-acf-field-wysiwyg.php:272
msgid "Click to initialize TinyMCE"
msgstr "Clique para inicializar o TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:266
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "HTML"

#: includes/fields/class-acf-field-wysiwyg.php:265
msgid "Visual"
msgstr "Visual"

#: includes/fields/class-acf-field-text.php:183
#: includes/fields/class-acf-field-textarea.php:220
msgid "Value must not exceed %d characters"
msgstr "O valor não deve exceder %d caracteres"

#: includes/fields/class-acf-field-text.php:118
#: includes/fields/class-acf-field-textarea.php:116
msgid "Leave blank for no limit"
msgstr "Deixe em branco para não limitar"

#: includes/fields/class-acf-field-text.php:117
#: includes/fields/class-acf-field-textarea.php:115
msgid "Character Limit"
msgstr "Limite de caracteres"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:197
#: includes/fields/class-acf-field-password.php:97
#: includes/fields/class-acf-field-range.php:231
#: includes/fields/class-acf-field-text.php:158
msgid "Appears after the input"
msgstr "Mostrado depois do campo"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:196
#: includes/fields/class-acf-field-password.php:96
#: includes/fields/class-acf-field-range.php:230
#: includes/fields/class-acf-field-text.php:157
msgid "Append"
msgstr "Suceder"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:187
#: includes/fields/class-acf-field-password.php:87
#: includes/fields/class-acf-field-range.php:221
#: includes/fields/class-acf-field-text.php:148
msgid "Appears before the input"
msgstr "Mostrado antes do campo"

#: includes/fields/class-acf-field-email.php:135
#: includes/fields/class-acf-field-number.php:186
#: includes/fields/class-acf-field-password.php:86
#: includes/fields/class-acf-field-range.php:220
#: includes/fields/class-acf-field-text.php:147
msgid "Prepend"
msgstr "Preceder"

#: includes/fields/class-acf-field-email.php:126
#: includes/fields/class-acf-field-number.php:167
#: includes/fields/class-acf-field-password.php:77
#: includes/fields/class-acf-field-text.php:138
#: includes/fields/class-acf-field-textarea.php:148
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Mostrado dentro do campo"

#: includes/fields/class-acf-field-email.php:125
#: includes/fields/class-acf-field-number.php:166
#: includes/fields/class-acf-field-password.php:76
#: includes/fields/class-acf-field-text.php:137
#: includes/fields/class-acf-field-textarea.php:147
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Texto predefinido"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-email.php:106
#: includes/fields/class-acf-field-number.php:117
#: includes/fields/class-acf-field-radio.php:196
#: includes/fields/class-acf-field-range.php:157
#: includes/fields/class-acf-field-text.php:98
#: includes/fields/class-acf-field-textarea.php:96
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:296
msgid "Appears when creating a new post"
msgstr "Mostrado ao criar um novo conteúdo"

#: includes/fields/class-acf-field-text.php:23
msgid "Text"
msgstr "Texto"

#: includes/fields/class-acf-field-relationship.php:735
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] ""
msgstr[1] ""

#: includes/fields/class-acf-field-post_object.php:378
#: includes/fields/class-acf-field-relationship.php:596
msgid "Post ID"
msgstr "ID do conteúdo"

#: includes/fields/class-acf-field-post_object.php:17
#: includes/fields/class-acf-field-post_object.php:377
#: includes/fields/class-acf-field-relationship.php:595
msgid "Post Object"
msgstr "Conteúdo"

#: includes/fields/class-acf-field-relationship.php:628
msgid "Maximum Posts"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:618
msgid "Minimum Posts"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:653
msgid "Featured Image"
msgstr "Imagem de destaque"

#: includes/fields/class-acf-field-relationship.php:649
msgid "Selected elements will be displayed in each result"
msgstr "Os elementos seleccionados serão mostrados em cada resultado."

#: includes/fields/class-acf-field-relationship.php:648
msgid "Elements"
msgstr "Elementos"

#: includes/fields/class-acf-field-relationship.php:582
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:630
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taxonomia"

#: includes/fields/class-acf-field-relationship.php:581
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Tipo de conteúdo"

#: includes/fields/class-acf-field-relationship.php:575
msgid "Filters"
msgstr "Filtros"

#: includes/fields/class-acf-field-page_link.php:447
#: includes/fields/class-acf-field-post_object.php:365
#: includes/fields/class-acf-field-relationship.php:568
msgid "All taxonomies"
msgstr "Todas as taxonomias"

#: includes/fields/class-acf-field-page_link.php:439
#: includes/fields/class-acf-field-post_object.php:357
#: includes/fields/class-acf-field-relationship.php:560
msgid "Filter by Taxonomy"
msgstr "Filtrar por taxonomia"

#: includes/fields/class-acf-field-page_link.php:417
#: includes/fields/class-acf-field-post_object.php:335
#: includes/fields/class-acf-field-relationship.php:538
msgid "All post types"
msgstr "Todos os tipos de conteúdo"

#: includes/fields/class-acf-field-page_link.php:409
#: includes/fields/class-acf-field-post_object.php:327
#: includes/fields/class-acf-field-relationship.php:530
msgid "Filter by Post Type"
msgstr "Filtrar por tipo de conteúdo"

#: includes/fields/class-acf-field-relationship.php:430
msgid "Search..."
msgstr "Pesquisar..."

#: includes/fields/class-acf-field-relationship.php:361
msgid "Select taxonomy"
msgstr "Seleccione taxonomia"

#: includes/fields/class-acf-field-relationship.php:353
msgid "Select post type"
msgstr "Seleccione tipo de conteúdo"

#: includes/fields/class-acf-field-relationship.php:58
#: assets/build/js/acf-input.js:3928 assets/build/js/acf-input.js:4214
msgid "No matches found"
msgstr "Nenhuma correspondência encontrada"

#: includes/fields/class-acf-field-relationship.php:57
#: assets/build/js/acf-input.js:3911 assets/build/js/acf-input.js:4193
msgid "Loading"
msgstr "A carregar"

#: includes/fields/class-acf-field-relationship.php:56
#: assets/build/js/acf-input.js:3816 assets/build/js/acf-input.js:4084
msgid "Maximum values reached ( {max} values )"
msgstr "Valor máximo alcançado ( valor {max} )"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Relação"

#: includes/fields/class-acf-field-file.php:280
#: includes/fields/class-acf-field-image.php:310
msgid "Comma separated list. Leave blank for all types"
msgstr ""
"Lista separada por vírgulas. Deixe em branco para permitir todos os tipos."

#: includes/fields/class-acf-field-file.php:279
#: includes/fields/class-acf-field-image.php:309
msgid "Allowed File Types"
msgstr ""

#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-image.php:273
msgid "Maximum"
msgstr "Máximo"

#: includes/fields/class-acf-field-file.php:147
#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-file.php:271
#: includes/fields/class-acf-field-image.php:264
#: includes/fields/class-acf-field-image.php:300
msgid "File size"
msgstr "Tamanho do ficheiro"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
msgid "Restrict which images can be uploaded"
msgstr "Restringir que imagens que ser carregadas"

#: includes/fields/class-acf-field-file.php:255
#: includes/fields/class-acf-field-image.php:237
msgid "Minimum"
msgstr "Mínimo"

#: includes/fields/class-acf-field-file.php:225
#: includes/fields/class-acf-field-image.php:203
msgid "Uploaded to post"
msgstr "Carregados no artigo"

#: includes/fields/class-acf-field-file.php:224
#: includes/fields/class-acf-field-image.php:202
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Todos"

#: includes/fields/class-acf-field-file.php:219
#: includes/fields/class-acf-field-image.php:197
msgid "Limit the media library choice"
msgstr "Limita a escolha da biblioteca de media."

#: includes/fields/class-acf-field-file.php:218
#: includes/fields/class-acf-field-image.php:196
msgid "Library"
msgstr "Biblioteca"

#: includes/fields/class-acf-field-image.php:329
msgid "Preview Size"
msgstr "Tamanho da pré-visualização"

#: includes/fields/class-acf-field-image.php:188
msgid "Image ID"
msgstr "ID da imagem"

#: includes/fields/class-acf-field-image.php:187
msgid "Image URL"
msgstr "URL da imagem"

#: includes/fields/class-acf-field-image.php:186
msgid "Image Array"
msgstr "Array da imagem"

#: includes/fields/class-acf-field-button-group.php:159
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-file.php:203
#: includes/fields/class-acf-field-link.php:149
#: includes/fields/class-acf-field-radio.php:206
msgid "Specify the returned value on front end"
msgstr "Especifica o valor devolvido na frente do site."

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:347
#: includes/fields/class-acf-field-file.php:202
#: includes/fields/class-acf-field-link.php:148
#: includes/fields/class-acf-field-radio.php:205
#: includes/fields/class-acf-field-taxonomy.php:674
msgid "Return Value"
msgstr "Valor devolvido"

#: includes/fields/class-acf-field-image.php:157
msgid "Add Image"
msgstr "Adicionar imagem"

#: includes/fields/class-acf-field-image.php:157
msgid "No image selected"
msgstr "Nenhuma imagem seleccionada"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:155
#: includes/fields/class-acf-field-image.php:137
#: includes/fields/class-acf-field-link.php:126 assets/build/js/acf.js:1563
#: assets/build/js/acf.js:1657
msgid "Remove"
msgstr "Remover"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:153
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:126
msgid "Edit"
msgstr "Editar"

#: includes/fields/class-acf-field-image.php:65 includes/media.php:55
#: assets/build/js/acf-input.js:6850 assets/build/js/acf-input.js:7336
msgid "All images"
msgstr "Todas as imagens"

#: includes/fields/class-acf-field-image.php:64
#: assets/build/js/acf-input.js:3179 assets/build/js/acf-input.js:3399
msgid "Update Image"
msgstr "Actualizar imagem"

#: includes/fields/class-acf-field-image.php:63
#: assets/build/js/acf-input.js:3178 assets/build/js/acf-input.js:3398
msgid "Edit Image"
msgstr "Editar imagem"

#: includes/fields/class-acf-field-image.php:62
#: assets/build/js/acf-input.js:3154 assets/build/js/acf-input.js:3373
msgid "Select Image"
msgstr "Seleccionar imagem"

#: includes/fields/class-acf-field-image.php:23
msgid "Image"
msgstr "Imagem"

#: includes/fields/class-acf-field-message.php:112
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Permite visualizar o código HTML como texto visível, em vez de o processar."

#: includes/fields/class-acf-field-message.php:111
msgid "Escape HTML"
msgstr "Mostrar HTML"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:164
msgid "No Formatting"
msgstr "Sem formatação"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:163
msgid "Automatically add &lt;br&gt;"
msgstr "Adicionar &lt;br&gt; automaticamente"

#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-textarea.php:162
msgid "Automatically add paragraphs"
msgstr "Adicionar parágrafos automaticamente"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:158
msgid "Controls how new lines are rendered"
msgstr "Controla como serão visualizadas novas linhas."

#: includes/fields/class-acf-field-message.php:96
#: includes/fields/class-acf-field-textarea.php:157
msgid "New Lines"
msgstr "Novas linhas"

#: includes/fields/class-acf-field-date_picker.php:224
#: includes/fields/class-acf-field-date_time_picker.php:211
msgid "Week Starts On"
msgstr "Semana começa em"

#: includes/fields/class-acf-field-date_picker.php:193
msgid "The format used when saving a value"
msgstr "O formato usado ao guardar um valor"

#: includes/fields/class-acf-field-date_picker.php:192
msgid "Save Format"
msgstr "Formato guardado"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Sem"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Anterior"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Seguinte"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Hoje"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Concluído"

#: includes/fields/class-acf-field-date_picker.php:23
msgid "Date Picker"
msgstr "Selecção de data"

#: includes/fields/class-acf-field-image.php:241
#: includes/fields/class-acf-field-image.php:277
#: includes/fields/class-acf-field-oembed.php:255
msgid "Width"
msgstr "Largura"

#: includes/fields/class-acf-field-oembed.php:252
#: includes/fields/class-acf-field-oembed.php:264
msgid "Embed Size"
msgstr "Tamanho da incorporação"

#: includes/fields/class-acf-field-oembed.php:212
msgid "Enter URL"
msgstr "Insira o URL"

#: includes/fields/class-acf-field-oembed.php:23
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:174
msgid "Text shown when inactive"
msgstr "Texto mostrado quando inactivo"

#: includes/fields/class-acf-field-true_false.php:173
msgid "Off Text"
msgstr "Texto desligado"

#: includes/fields/class-acf-field-true_false.php:158
msgid "Text shown when active"
msgstr "Texto mostrado quando activo"

#: includes/fields/class-acf-field-true_false.php:157
msgid "On Text"
msgstr "Texto ligado"

#: includes/fields/class-acf-field-select.php:428
#: includes/fields/class-acf-field-true_false.php:189
msgid "Stylized UI"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-checkbox.php:337
#: includes/fields/class-acf-field-color_picker.php:148
#: includes/fields/class-acf-field-email.php:105
#: includes/fields/class-acf-field-number.php:116
#: includes/fields/class-acf-field-radio.php:195
#: includes/fields/class-acf-field-range.php:156
#: includes/fields/class-acf-field-select.php:359
#: includes/fields/class-acf-field-text.php:97
#: includes/fields/class-acf-field-textarea.php:95
#: includes/fields/class-acf-field-true_false.php:137
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:295
msgid "Default Value"
msgstr "Valor por omissão"

#: includes/fields/class-acf-field-true_false.php:128
msgid "Displays text alongside the checkbox"
msgstr "Texto mostrado ao lado da caixa de selecção"

#: includes/fields/class-acf-field-message.php:24
#: includes/fields/class-acf-field-message.php:86
#: includes/fields/class-acf-field-true_false.php:127
msgid "Message"
msgstr "Mensagem"

#: includes/assets.php:352 includes/fields/class-acf-field-true_false.php:81
#: includes/fields/class-acf-field-true_false.php:177
#: assets/build/js/acf.js:1740 assets/build/js/acf.js:1857
msgid "No"
msgstr "Não"

#: includes/assets.php:351 includes/fields/class-acf-field-true_false.php:78
#: includes/fields/class-acf-field-true_false.php:161
#: assets/build/js/acf.js:1739 assets/build/js/acf.js:1856
msgid "Yes"
msgstr "Sim"

#: includes/fields/class-acf-field-true_false.php:23
msgid "True / False"
msgstr "Verdadeiro / Falso"

#: includes/fields/class-acf-field-group.php:421
msgid "Row"
msgstr "Linha"

#: includes/fields/class-acf-field-group.php:420
msgid "Table"
msgstr "Tabela"

#: includes/admin/post-types/admin-field-group.php:128
#: includes/fields/class-acf-field-group.php:419
msgid "Block"
msgstr "Bloco"

#: includes/fields/class-acf-field-group.php:414
msgid "Specify the style used to render the selected fields"
msgstr "Especifica o estilo usado para mostrar os campos seleccionados."

#: includes/fields.php:337 includes/fields/class-acf-field-button-group.php:205
#: includes/fields/class-acf-field-checkbox.php:410
#: includes/fields/class-acf-field-group.php:413
#: includes/fields/class-acf-field-radio.php:279
msgid "Layout"
msgstr "Layout"

#: includes/fields/class-acf-field-group.php:397
msgid "Sub Fields"
msgstr "Subcampos"

#: includes/fields/class-acf-field-group.php:23
msgid "Group"
msgstr "Grupo"

#: includes/fields/class-acf-field-google-map.php:226
msgid "Customize the map height"
msgstr "Personalizar a altura do mapa"

#: includes/fields/class-acf-field-google-map.php:225
#: includes/fields/class-acf-field-image.php:252
#: includes/fields/class-acf-field-image.php:288
#: includes/fields/class-acf-field-oembed.php:267
msgid "Height"
msgstr "Altura"

#: includes/fields/class-acf-field-google-map.php:214
msgid "Set the initial zoom level"
msgstr "Definir o nível de zoom inicial"

#: includes/fields/class-acf-field-google-map.php:213
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:187
#: includes/fields/class-acf-field-google-map.php:200
msgid "Center the initial map"
msgstr "Centrar o mapa inicial"

#: includes/fields/class-acf-field-google-map.php:186
#: includes/fields/class-acf-field-google-map.php:199
msgid "Center"
msgstr "Centrar"

#: includes/fields/class-acf-field-google-map.php:157
msgid "Search for address..."
msgstr "Pesquisar endereço..."

#: includes/fields/class-acf-field-google-map.php:154
msgid "Find current location"
msgstr "Encontrar a localização actual"

#: includes/fields/class-acf-field-google-map.php:153
msgid "Clear location"
msgstr "Limpar localização"

#: includes/fields/class-acf-field-google-map.php:152
#: includes/fields/class-acf-field-relationship.php:580
msgid "Search"
msgstr "Pesquisa"

#: includes/fields/class-acf-field-google-map.php:59
#: assets/build/js/acf-input.js:2838 assets/build/js/acf-input.js:3026
msgid "Sorry, this browser does not support geolocation"
msgstr "Desculpe, este navegador não suporta geolocalização."

#: includes/fields/class-acf-field-google-map.php:23
msgid "Google Map"
msgstr "Mapa do Google"

#: includes/fields/class-acf-field-date_picker.php:204
#: includes/fields/class-acf-field-date_time_picker.php:192
#: includes/fields/class-acf-field-time_picker.php:124
msgid "The format returned via template functions"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:172
#: includes/fields/class-acf-field-date_picker.php:203
#: includes/fields/class-acf-field-date_time_picker.php:191
#: includes/fields/class-acf-field-image.php:180
#: includes/fields/class-acf-field-post_object.php:372
#: includes/fields/class-acf-field-relationship.php:590
#: includes/fields/class-acf-field-select.php:370
#: includes/fields/class-acf-field-time_picker.php:123
#: includes/fields/class-acf-field-user.php:66
msgid "Return Format"
msgstr "Formato devolvido"

#: includes/fields/class-acf-field-date_picker.php:182
#: includes/fields/class-acf-field-date_picker.php:213
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:115
#: includes/fields/class-acf-field-time_picker.php:131
msgid "Custom:"
msgstr "Personalizado:"

#: includes/fields/class-acf-field-date_picker.php:174
#: includes/fields/class-acf-field-date_time_picker.php:174
#: includes/fields/class-acf-field-time_picker.php:108
msgid "The format displayed when editing a post"
msgstr "O formato de visualização ao editar um conteúdo"

#: includes/fields/class-acf-field-date_picker.php:173
#: includes/fields/class-acf-field-date_time_picker.php:173
#: includes/fields/class-acf-field-time_picker.php:107
msgid "Display Format"
msgstr "Formato de visualização"

#: includes/fields/class-acf-field-time_picker.php:23
msgid "Time Picker"
msgstr "Selecção de hora"

#. translators: counts for inactive field groups
#: acf.php:496
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: acf.php:457
msgid "No Fields found in Trash"
msgstr "Nenhum campo encontrado no lixo"

#: acf.php:456
msgid "No Fields found"
msgstr "Nenhum campo encontrado"

#: acf.php:455
msgid "Search Fields"
msgstr "Pesquisar campos"

#: acf.php:454
msgid "View Field"
msgstr "Ver campo"

#: acf.php:453 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Novo campo"

#: acf.php:452
msgid "Edit Field"
msgstr "Editar campo"

#: acf.php:451
msgid "Add New Field"
msgstr "Adicionar novo campo"

#: acf.php:449
msgid "Field"
msgstr "Campo"

#: acf.php:448 includes/admin/post-types/admin-field-group.php:149
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Campos"

#: acf.php:423
msgid "No Field Groups found in Trash"
msgstr "Nenhum grupo de campos encontrado no lixo"

#: acf.php:422
msgid "No Field Groups found"
msgstr "Nenhum grupo de campos encontrado"

#: acf.php:421
msgid "Search Field Groups"
msgstr "Pesquisar grupos de campos"

#: acf.php:420
msgid "View Field Group"
msgstr "Ver grupo de campos"

#: acf.php:419
msgid "New Field Group"
msgstr "Novo grupo de campos"

#: acf.php:418
msgid "Edit Field Group"
msgstr "Editar grupo de campos"

#: acf.php:417
msgid "Add New Field Group"
msgstr "Adicionar novo grupo de campos"

#: acf.php:416 acf.php:450
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Adicionar novo"

#: acf.php:415
msgid "Field Group"
msgstr "Grupo de campos"

#: acf.php:414 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "Grupos de campos"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Personalize o WordPress com campos intuitivos, poderosos e profissionais."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:93
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"
