<?php
// PHP Settings
include_once 'functions/php-ini.php';

// Send PHP errors to Slack
include_once 'services/maintenance/SlackNotifications.php';

// General
add_action('pre_get_posts', 'render_page_without_post_type_slug');
// Remove type from permalink
function render_page_without_post_type_slug($query)
{
    if (!$query->is_main_query())
        return;

    if (2 != count($query->query) || !isset($query->query['page']))
        return;

    if (!empty($query->query['name'])) {

        $query->set('post_type', ['post', CAMPAIGN_SLUG, ACQUISITION_SLUG, DYNAMIC_SLUG, RESPONSIBLE_GAMING_SLUG, SEO_PAGE_SLUG, 'page']);
    }
}

// Admin init extras
add_action('admin_init', 'admin_init_extras');
function admin_init_extras() {
    // Proxy URLs workaround
    $proxyType = '';

    if(SITE_TYPE == SITE_TYPE_GO) {
        $proxyType = '/go';
    } elseif(SITE_TYPE == SITE_TYPE_RG) {
        $proxyType = '/rg';
    } elseif(SITE_TYPE == SITE_TYPE_FAQ) {
        $proxyType = '/faq';
    }

    if($proxyType) {
        $_SERVER['REQUEST_URI'] = str_replace('/wp-admin/', $proxyType . '/wp-admin/',  (string) $_SERVER['REQUEST_URI']);
    }

    // Proxy sites admin canonical URL fix
    remove_action( 'admin_head', 'wp_admin_canonical_url' );

    // Disable autosave
    wp_deregister_script('autosave');
}


// Disable WP API for non-logged-in users
add_filter('rest_authentication_errors', function ($result) {
    if (!empty($result)) {
        return $result;
    }
    if (!is_user_logged_in()) {
        return new WP_Error('rest_not_logged_in', 'You are not currently logged in.', ['status' => 401]);
    }
    return $result;
});

// require_once __DIR__ . '/vendor/autoload.php'; // not sure about performance with this

/*
Query all options to be used for acf fields that are saved into wp_options table
*/
global $wpdb, $all_options;
$all_options = $wpdb->get_results("SELECT option_name, option_value FROM {$wpdb->options} WHERE option_name LIKE 'options_%'", 'OBJECT_K');

// Add Main javascript and background inline style // TODO: maybe have the inline bg style in a enqueue_style file
include 'functions/enqueue-scripts.php';

// ACF config handler
include_once 'functions/acf-config.php';

// Global brand config handler
include_once 'functions/config.php';

// Global functions
include_once 'functions/global.php';

// Global variables
include_once 'global-variables.php';

// Utility functions
include_once 'functions/utilities.php';

// Brand Config - Always prioritize child theme config
if (file_exists(get_stylesheet_directory() . '/services/config.php')) {
    include_once(get_stylesheet_directory() . '/services/config.php');
} elseif (file_exists(get_template_directory() . '/services/config.php')) {
    include_once(get_template_directory() . '/services/config.php');
}

// Load Services
require_once('services/loader.php');

// Campaign related utility function
// if(isFeatureActive('campaigns')) {
//     add_action('restrict_manage_posts', 'filter_by_template');
// }

// Hide the admin bar on FE
add_filter('show_admin_bar', '__return_false');

// Public functions
include_once 'functions/public.php';
include_once 'functions/datetime.php';
include_once 'functions/vector.php';
include_once 'functions/get-class.php';
include_once 'functions/get-field-tweaked.php';
include_once 'functions/recaptcha.php';
include_once 'functions/seo.php';
// include_once 'functions/tracking.php';
include_once 'functions/acf-backwards-compatibility/acf-backwards.php';
include_once 'functions/acf-auto-class.php';
include_once 'functions/acf-fields-filters.php';
include_once 'functions/performance.php';

// Player related logic
include_once 'services/player/player_service.php';

// Brand API to get footer links etc.
include_once 'services/other/BrandAPI.php';

// Acquisition related
if (isFeatureActive('campaigns/acq'))
    include 'campaigns/acq/functions.php';

// Dynamic related
if (isFeatureActive('campaigns/dynamic'))
    include 'campaigns/dynamic/functions.php';

// CRM
if (isFeatureActive('campaigns/crm'))
    include 'campaigns/crm/functions.php';

// Start page
if (isFeatureActive('start-page'))
    include 'start-page/functions.php';

// AD Banner
if (isFeatureActive('ad-banner'))
    include 'ad-banner/functions.php';

// Help page
if (isFeatureActive('help-page'))
    include 'help-page/functions.php';

// Responsible gaming
if (isFeatureActive('responsible-gaming'))
    include 'responsible-gaming/functions.php';

// Quiz post type functions
if (isFeatureActive('games/quiz'))
    include 'games/quiz/functions.php';

// Letter game post type
if (isFeatureActive('games/letter-game'))
    include 'games/letter-game/functions.php';

// Casino Games Lib post type
include 'services/casino-games/functions.php';

// SEO Pages
include 'seo-page/functions.php';

// Guides functions
include 'guides/functions.php';

// Define taxonomies on theme switch
include 'functions/add-templates-taxonomy.php';

// Required plugins
include 'functions/required-plugins.php';

// Plugin failsafe mechanism
include 'functions/auto-enable-plugins.php';

// Register desktop menu and hamburger mobile menu
register_nav_menus([
    'menu-1' => esc_html__('Primary', 'phoenix'),
    'mobile-menu' => esc_html__('Mobile Menu', 'phoenix'),
]);

// Register menu for new tabbed global nav links
if(!empty(AUTHORITIES[CURRENT_BRAND])) {
    foreach(array_keys(AUTHORITIES[CURRENT_BRAND]) as $lang) {
        // Register mobile menu type
        register_nav_menus([
            'mobile-menu_' . $lang=> esc_html__('Mobile Menu - ' . $lang, 'phoenix'),
            // 'desktop-menu_' . $lang => esc_html__('Desktop Menu - ' . $lang, 'phoenix'), // In case we wanna sync desktop menu in the future
        ]);
    }
}

// Blog
if (isFeatureActive('blog')) {
    add_post_type_support('page', 'excerpt');
    include 'blog/functions.php';
}


if (isFeatureActive('api/sportsbook')) {
    include 'services/sportsbook/loader.php';
    include 'shortcodes/get-odds.php';
}

if (isFeatureActive('api/jackpots')) {
    include 'services/api-jackpots.php';
    include 'shortcodes/get-jackpot.php';
    include 'shortcodes/jackpotometer.php';
}

// Admin functions
include_once 'admin/functions-admin.php';

// Shortcodes
include 'shortcodes/button.php';
include 'shortcodes/card.php';
include 'shortcodes/dynamic-content.php';
include 'shortcodes/player-api.php';

// CTA short code
if (isFeatureActive('shortcodes/cta')) {
    include 'shortcodes/cta.php';
}

// Gutenberg Blocks includer for Dynamic Pages
include 'functions/include-dynamic-blocks.php';

// Theme support
add_theme_support('title-tag');
add_theme_support('post-thumbnails');

// Remove shortlink from head
function remove_shortlink()
{
    remove_action('wp_head', 'wp_shortlink_wp_head', 10);
    remove_action('template_redirect', 'wp_shortlink_header', 11);
}

add_filter('after_setup_theme', 'remove_shortlink');

// Shortcodes in textarea
add_filter('acf/format_value/type=text', 'do_shortcode');
add_filter('acf/format_value/type=textarea', 'do_shortcode');
add_filter('acf/format_value/type=wysiwyg', 'do_shortcode');

// Cron schedule intervals
function my_add_intervals($schedules)
{
    // Add a weekly interval.
    $schedules['weekly'] = [
        'interval' => 604800,
        'display' => __('Once Weekly'),
    ];

    // Add a montly interval.
    $schedules['half_an_hour'] = [
        'interval' => 30 * 60,
        'display' => __('Each half an hour'),
    ];

    $schedules["10_mins"] = [
        'interval' => 10 * 60,
        'display' => __('Once every 10 minutes')
    ];

    return $schedules;
}

add_filter('cron_schedules', 'my_add_intervals');

// Embedded element size
function crunchify_embed_defaults($embed_size)
{
    $embed_size['width'] = 610;
    $embed_size['height'] = '100%';
    return $embed_size;
}

add_filter('embed_defaults', 'crunchify_embed_defaults');

// Responsive container
function wrap_embed_with_div($html, $url, $attr)
{
    return '<div class="responsive-container">' . $html . '</div>';
}
add_filter('embed_oembed_html', 'wrap_embed_with_div', 10, 3);

include_once 'functions/body-class.php';

// Add html5 support to fix w3 markup validation issues
add_action('after_setup_theme','add_html5_support');
function add_html5_support() {
    add_theme_support( 'html5', [ 'script', 'style' ] );
}

// Remove RSD since no-one is using Really Simple Discovery (RSD) for publishing/reading content anymore
remove_action( 'wp_head', 'rsd_link' );

// No-one is using Windows Live Writer
remove_action( 'wp_head', 'wlwmanifest_link' );

// We dont want category, tag feed urls (these called extra feed links) to appear in page source for SEO, this doesnt affect main blog feed url
remove_action( 'wp_head', 'feed_links_extra', 3 );

// Hide wp version for black-hats
remove_action( 'wp_head', 'wp_generator' );