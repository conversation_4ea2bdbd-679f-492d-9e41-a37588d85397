<?php
// Always prioritize child theme's config.json over parent theme's config.json
$configPath = '';
if (file_exists(get_stylesheet_directory() . '/services/config.json')) {
    $configPath = get_stylesheet_directory() . '/services/config.json';
} elseif (file_exists(get_template_directory() . '/services/config.json')) {
    $configPath = get_template_directory() . '/services/config.json';
}

if ($configPath) {
    $configJson = file_get_contents($configPath);
    $configJson = json_decode($configJson, true);
    extract($configJson);
    unset($configJson);
}
